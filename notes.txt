import { Inbox, Mail, Menu as MenuIcon } from "@mui/icons-material";
// import {
// 	AppBar,
// 	Box,
// 	CssBaseline,
// 	Drawer,
// 	IconButton,
// 	List,
// 	ListItem,
// 	ListItemButton,
// 	ListItemIcon,
// 	ListItemText,
// 	Toolbar,
// 	Typography,
// } from "@mui/material";
// import * as React from "react";
// import { Outlet } from "react-router";

// const drawerWidth = 240;

// export function AdminLayout() {
// 	const [mobileOpen, setMobileOpen] = React.useState(false);

// 	const handleDrawerToggle = () => {
// 		setMobileOpen(!mobileOpen);
// 	};

// 	const drawerContent = (
// 		<div>
// 			<Toolbar />
// 			<Box sx={{ overflow: "auto" }}>
// 				<List>
// 					{["Dashboard", "Users", "Settings"].map((text, index) => (
// 						<ListItem key={text} disablePadding>
// 							<ListItemButton>
// 								<ListItemIcon sx={{ color: "text.secondary" }}>{index % 2 === 0 ? <Inbox /> : <Mail />}</ListItemIcon>
// 								<ListItemText primary={text} />
// 							</ListItemButton>
// 						</ListItem>
// 					))}
// 				</List>
// 			</Box>
// 		</div>
// 	);

// 	return (
// 		<Box sx={{ display: "flex" }}>
// 			<CssBaseline />
// 			<AppBar
// 				position="fixed"
// 				color="primary"
// 				sx={{
// 					width: { md: `calc(100% - ${drawerWidth}px)` },
// 					ml: { md: `${drawerWidth}px` },
// 				}}
// 			>
// 				<Toolbar>
// 					<IconButton
// 						color="inherit"
// 						aria-label="open drawer"
// 						edge="start"
// 						onClick={handleDrawerToggle}
// 						sx={{ mr: 2, display: { md: "none" } }}
// 					>
// 						<MenuIcon />
// 					</IconButton>
// 					<Typography variant="h6" noWrap component="div">
// 						Admin Panel
// 					</Typography>
// 				</Toolbar>
// 			</AppBar>
// 			<Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }} aria-label="mailbox folders">
// 				{/* Temporary Drawer for mobile */}
// 				<Drawer
// 					variant="temporary"
// 					open={mobileOpen}
// 					onClose={handleDrawerToggle}
// 					ModalProps={{
// 						keepMounted: true, // Better open performance on mobile.
// 					}}
// 					sx={{
// 						display: { xs: "block", md: "none" },
// 						"& .MuiDrawer-paper": { boxSizing: "border-box", width: drawerWidth },
// 					}}
// 				>
// 					{drawerContent}
// 				</Drawer>
// 				{/* Permanent Drawer for desktop */}
// 				<Drawer
// 					variant="permanent"
// 					sx={{
// 						display: { xs: "none", md: "block" },
// 						"& .MuiDrawer-paper": { boxSizing: "border-box", width: drawerWidth },
// 					}}
// 					open
// 				>
// 					{drawerContent}
// 				</Drawer>
// 			</Box>
// 			<Box component="main" sx={{ flexGrow: 1, p: 3, width: { md: `calc(100% - ${drawerWidth}px)` } }}>
// 				<Toolbar />
// 				<Outlet />
// 			</Box>
// 		</Box>
// 	);
// }
