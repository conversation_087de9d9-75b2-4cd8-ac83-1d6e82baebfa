{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "generate-types": "openapi-typescript http://localhost:5000/openapi.yaml -o src/shared/types/schema.d.ts", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/nunito": "^5.2.6", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/x-date-pickers": "^8.7.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-table": "^8.21.3", "@types/swiper": "^5.4.3", "axios": "^1.9.0", "dayjs": "^1.11.13", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "react": "^19.1.0", "react-d3-tree": "^3.6.6", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-leaflet": "^5.0.0", "react-router": "^7.6.1", "react-router-dom": "^7.6.2", "swiper": "^11.2.10", "use-debounce": "^10.0.5", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.5", "@types/leaflet": "^1.9.20", "@types/node": "^22.15.29", "@types/nprogress": "^0.2.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "cross-env": "^7.0.3", "globals": "^16.0.0", "openapi-typescript": "^7.8.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}