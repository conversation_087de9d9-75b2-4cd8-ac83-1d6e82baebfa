{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.5/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "tab", "attributePosition": "auto", "indentWidth": 2, "lineWidth": 80, "lineEnding": "lf"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off"}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "complexity": {"noForEach": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}