import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { AdministratorUserFormContent } from "../components/AdministratorUserFormContent";
import { useCreateAdministratorUser } from "../hooks/useCreateAdministratorUser";
import { useGetUserOptions } from "../hooks/useGetUserOptions";
import {
	type CreateAdministratorUserSchema,
	createAdministratorUserSchema,
} from "../validation/createAdministratorUserSchema";

const CreateAdministratorUserPage: React.FC = () => {
	const { data: userOptionsData, isLoading } = useGetUserOptions();

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateAdministratorUser({
		onSuccessCallback: (data) =>
			handleSuccess(data.message, "/users/administrators"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateAdministratorUserSchema) => {
		mutation.mutate(data);
	};

	return (
		<FormProvider
			schema={createAdministratorUserSchema}
			defaultValues={{
				userId: "",
				name: "",
				email: "",
				password: undefined,
			}}
			onSubmit={onSubmit}
		>
			<AdministratorUserFormContent
				label="Buat Administrator User Baru"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateAdministratorUserPage;
