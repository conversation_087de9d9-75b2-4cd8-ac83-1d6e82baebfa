import { useGetRoleOptions } from "@/modules/roles/hooks/useGetRoleOptions";
import { useGetWorksiteOptions } from "@/modules/worksite/hooks/useGetWorksiteOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { UserFormContent } from "../components/UserFormContent";
import { useCreateUser } from "../hooks/useCreateUser";
import {
	type CreateUserSchema,
	createUserSchema,
} from "../validation/createUserSchema";

const CreateUserPage: React.FC = () => {
	const roles = useGetRoleOptions();
	const worksites = useGetWorksiteOptions();
	const isLoading = roles.isLoading || worksites.isLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();

	const mutation = useCreateUser({
		onSuccessCallback: (data) =>
			handleSuccess(data.message, "/users"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateUserSchema) => {
		mutation.mutate({ ...data, supervisorId: data.supervisorId ?? null });
	};

	return (
		<FormProvider
			schema={createUserSchema}
			defaultValues={{
				name: "",
				email: "",
				mobileNumber: "",
				nik: "",
				password: undefined,
				roleId: "",
				supervisorId: null,
				worksites: [],
			}}
			onSubmit={onSubmit}
		>
			<UserFormContent
				label="Buat User Baru"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				roleOptions={roles.data?.data as SelectOption[]}
				worksiteOptions={worksites.data?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateUserPage;
