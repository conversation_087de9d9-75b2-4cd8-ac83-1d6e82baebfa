import { useParams } from "react-router";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { AdministratorUserFormContent } from "../components/AdministratorUserFormContent";
import { useGetAdministratorUser } from "../hooks/useGetAdministratorUser";
import { useGetUserOptions } from "../hooks/useGetUserOptions";
import { useUpdateAdministratorUser } from "../hooks/useUpdateAdministratorUser";
import {
	type UpdateAdministratorUserSchema,
	updateAdministratorUserSchema,
} from "../validation/updateAdministratorUserSchema";

const EditAdministratorUserPage: React.FC = () => {
	const { administratorUserId } = useParams();

	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();
	const { data: administratorUserData, isLoading: isAdministratorUserLoading } =
		useGetAdministratorUser(administratorUserId as string);

	const isLoading = isAdministratorUserLoading || isUserOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateAdministratorUser({
		onSuccessCallback: (data) =>
			handleSuccess(data.message, "/users/administrators"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateAdministratorUserSchema) => {
		mutation.mutate({
			administratorUserId: administratorUserId as string,
			payload: data,
		});
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !administratorUserData?.data) {
		return (
			<NotFoundPage
				resourceType="User"
				redirectTo="/users/administrators"
			/>
		);
	}

	// Prepare default values from fetched user data
	const defaultValues: UpdateAdministratorUserSchema = {
		name: administratorUserData?.data.name || "",
		email: administratorUserData?.data.email || "",
		password: undefined,
		userId: administratorUserData?.data.userId || "",
	};

	return (
		<FormProvider
			schema={updateAdministratorUserSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<AdministratorUserFormContent
				label="Edit Administrator User"
				isLoading={false}
				isSubmitting={false}
				userOptions={userOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditAdministratorUserPage;
