import { useParams } from "react-router";
import { useGetRoleOptions } from "@/modules/roles/hooks/useGetRoleOptions";
import { useGetWorksiteOptions } from "@/modules/worksite/hooks/useGetWorksiteOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { UserFormContent } from "../components/UserFormContent";
import { useGetUser } from "../hooks/useGetUser";
import { useUpdateUser } from "../hooks/useUpdateUser";
import {
	type UpdateUserSchema,
	updateUserSchema,
} from "../validation/updateUserSchema";

const EditUserPage: React.FC = () => {
	const { userId } = useParams();

	const { data: roleOptionsData, isLoading: isRoleLoading } =
		useGetRoleOptions();

	const { data: worksiteOptionsData, isLoading: isWorksiteLoading } =
		useGetWorksiteOptions();

	const { data: userData, isLoading: isUserLoading } = useGetUser(
		userId as string,
	);

	const isLoading = isRoleLoading || isWorksiteLoading || isUserLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateUser({
		onSuccessCallback: (data) => handleSuccess(data.message, "/users"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateUserSchema) => {
		mutation.mutate({
			userId: userId as string,
			payload: { ...data, supervisorId: data.supervisorId ?? null },
		});
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !userData?.data) {
		return <NotFoundPage resourceType="User" redirectTo="/users" />;
	}

	// Prepare default values from fetched user data
	const defaultValues: UpdateUserSchema = {
		name: userData?.data.name || "",
		email: userData?.data.email || "",
		mobileNumber: userData?.data.mobileNumber || "",
		nik: userData?.data.nik || "",
		password: undefined,
		roleId: userData?.data.roleId || "",
		supervisorId: userData?.data.supervisorId || undefined,
		worksites: userData?.data.worksites || [],
	};

	return (
		<FormProvider
			schema={updateUserSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<UserFormContent
				label="Edit User"
				userId={userId as string}
				isLoading={false}
				isSubmitting={mutation.isPending}
				roleOptions={roleOptionsData?.data as SelectOption[]}
				worksiteOptions={worksiteOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditUserPage;
