import { apiClient } from "@/shared/api/apiClient";
import type {
	UpdateAdministratorUserPayload,
	UpdateAdministratorUserResponse,
} from "@/shared/types/api";

export const updateAdministratorUser = async (
	administratorUserId: string,
	payload: UpdateAdministratorUserPayload,
) => {
	const result = await apiClient.put<UpdateAdministratorUserResponse>(
		`/api/v1/admin/administrator-users/${administratorUserId}`,
		payload,
	);
	return result;
};
