import {
	<PERSON>,
	But<PERSON>,
	FormHelperText,
	Grid,
	Stack,
	Typography,
} from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";

export const AdministratorUserFormContent: React.FC<{
	isLoading: boolean;
	isSubmitting: boolean;
	userOptions: SelectOption[];
	label: string;
}> = ({ isLoading, isSubmitting, userOptions, label }) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Stack
					direction="row"
					alignItems="center"
					justifyContent="space-between"
				>
					<Typography
						variant="body1"
						sx={{ fontWeight: 700, fontSize: "1.25rem" }}
					>
						Data Admin
					</Typography>
				</Stack>

				<Box sx={{ mt: 4 }}>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="name"
								label="Nama"
								placeholder="Masukkan nama kariawan"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="email"
								label="Email"
								placeholder="Masukkan email"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="password"
								label="Password"
								placeholder="Masukkan password"
								helperText={
									<FormHelperText error={false}>
										Jangan masukkan password baru jika tidak ingin mengganti
										password
									</FormHelperText>
								}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="userId"
								label="User"
								placeholder="Cari dan pilih opsi"
								options={userOptions}
								isLoading={isLoading}
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/users/administrators"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
