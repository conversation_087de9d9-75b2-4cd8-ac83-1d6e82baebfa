import * as z from "zod";

export const createUserSchema = z.object({
	name: z.string().min(1, "Minimal 1 karakter"),
	email: z.email("Format email tidak sesuai"),
	nik: z.string().min(1, "Minimal 1 karakter"),
	mobileNumber: z.string().min(1, "Minimal 1 karakter"),
	password: z.string().optional(),
	roleId: z.string().min(1, "Harus memilih jabatan"),
	supervisorId: z.string().nullable(),
	worksites: z.optional(
		z.array(
			z.object({
				id: z.string().min(1, { error: "Minimal 1 karakter" }),
				isMain: z.coerce.boolean({
					error: "Harus berupa boolean, true atau false",
				}),
			}),
		),
	),
});
export type CreateUserSchema = z.infer<typeof createUserSchema>;
