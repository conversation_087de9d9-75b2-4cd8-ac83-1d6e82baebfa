import * as z from "zod";

export const createAdministratorUserSchema = z.object({
	userId: z
		.string()
		.nonempty({ error: "Wajib pilih user yang terhubung dengan akun admin ini" }),
	name: z.string().min(1, { error: "Minimal 1 karakter" }),
	email: z.email({ error: "Format email tidak valid" }),
	password: z.string().optional(),
});
export type CreateAdministratorUserSchema = z.infer<
	typeof createAdministratorUserSchema
>;
