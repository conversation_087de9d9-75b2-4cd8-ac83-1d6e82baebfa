import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateUserPayload,
	UpdateUserResponse,
} from "@/shared/types/api";
import { updateUser } from "../api/updateUser";

interface UseUpdateUserOptions {
	onSuccessCallback?: (data: UpdateUserResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateUser = (options?: UseUpdateUserOptions) => {
	return useMutation<
		UpdateUserResponse,
		AxiosError<BaseErrorResponse>,
		{ userId: string; payload: UpdateUserPayload }
	>({
		mutationFn: ({ userId, payload }) => {
			return updateUser(userId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
