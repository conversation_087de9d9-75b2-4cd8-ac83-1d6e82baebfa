import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type {
	BaseErrorResponse,
	GetUserOptionsResponse,
} from "@/shared/types/api";
import { getUserOptions } from "../api/getUserOptions";
import type { AxiosError } from "axios";

export const useGetUserOptions = (
	options?: Omit<
		UseQueryOptions<GetUserOptionsResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getUserOptions"],
		queryFn: getUserOptions,
		...options,
	});
};
