import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetUserTreeResponse,
} from "@/shared/types/api";
import { getUserTree } from "../api/getUserTree";

export const useGetUserTree = (
	options?: Omit<
		UseQueryOptions<GetUserTreeResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getUserTree"],
		queryFn: getUserTree,
		...options,
	});
};
