import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateAdministratorUserPayload,
	UpdateAdministratorUserResponse,
} from "@/shared/types/api";
import { updateAdministratorUser } from "../api/updateAdministratorUser";

interface UseUpdateAdministratorUserOptions {
	onSuccessCallback?: (data: UpdateAdministratorUserResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateAdministratorUser = (
	options?: UseUpdateAdministratorUserOptions,
) => {
	return useMutation<
		UpdateAdministratorUserResponse,
		AxiosError<BaseErrorResponse>,
		{ administratorUserId: string; payload: UpdateAdministratorUserPayload }
	>({
		mutationFn: ({ administratorUserId, payload }) => {
			return updateAdministratorUser(administratorUserId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
