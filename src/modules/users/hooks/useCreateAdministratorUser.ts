import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateAdministratorUserPayload,
	CreateAdministratorUserResponse,
} from "@/shared/types/api";
import { createAdministratorUser } from "../api/createAdministratorUser";

interface UseCreateAdministratorUserOptions {
	onSuccessCallback?: (data: CreateAdministratorUserResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateAdministratorUser = (options?: UseCreateAdministratorUserOptions) => {
	return useMutation<
		CreateAdministratorUserResponse,
		AxiosError<BaseErrorResponse>,
		CreateAdministratorUserPayload
	>({
		mutationFn: createAdministratorUser,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
