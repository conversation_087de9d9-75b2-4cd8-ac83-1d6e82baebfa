import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetAdministratorUserResponse,
} from "@/shared/types/api";
import { getAdministratorUser } from "../api/getAdministratorUser";

export const useGetAdministratorUser = (
	administratorUserId: string,
	options?: Omit<
		UseQueryOptions<
			GetAdministratorUserResponse,
			AxiosError<BaseErrorResponse>
		>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getAdministratorUser", administratorUserId],
		queryFn: async () => {
			return getAdministratorUser(administratorUserId);
		},
		...options,
	});
};
