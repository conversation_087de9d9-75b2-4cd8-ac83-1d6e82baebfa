import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateUserPayload,
	CreateUserResponse,
} from "@/shared/types/api";
import { createUser } from "../api/createUser";

interface UseCreateUserOptions {
	onSuccessCallback?: (data: CreateUserResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateUser = (options?: UseCreateUserOptions) => {
	return useMutation<
		CreateUserResponse,
		AxiosError<BaseErrorResponse>,
		CreateUserPayload
	>({
		mutationFn: createUser,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
