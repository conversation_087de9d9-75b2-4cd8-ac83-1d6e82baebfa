import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	BulkActionPayload,
	BulkActionResponse,
} from "@/shared/types/api";
import { bulkActionAdministratorUser } from "../api/bulkActionAdministratorUser";

interface UseBulkActionAdministratorUser {
	onSuccessCallback?: (data: BulkActionResponse | Blob) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useBulkActionAdministratorUser = (
	options?: UseBulkActionAdministratorUser,
) => {
	return useMutation<
		BulkActionResponse | Blob,
		AxiosError<BaseErrorResponse>,
		BulkActionPayload
	>({
		mutationFn: bulkActionAdministratorUser,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
