import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type { BaseErrorResponse, GetUserResponse } from "@/shared/types/api";
import { getUser } from "../api/getUser";

export const useGetUser = (
	userId: string,
	options?: Omit<
		UseQueryOptions<GetUserResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getUser", userId],
		queryFn: async () => {
			return getUser(userId);
		},
		...options,
	});
};
