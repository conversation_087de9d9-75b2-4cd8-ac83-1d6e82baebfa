import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetWorksiteOptionsResponse,
} from "@/shared/types/api";
import { getWorksiteOptions } from "../api/getWorksiteOptions";

export const useGetWorksiteOptions = (
	options?: Omit<
		UseQueryOptions<GetWorksiteOptionsResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getWorksiteOptions"],
		queryFn: async () => {
			return getWorksiteOptions();
		},
		...options,
	});
};
