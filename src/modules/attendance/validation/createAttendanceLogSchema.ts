import * as z from "zod";
import { AttendanceStatus, AttendanceType } from "../constants";

export const createAttendanceLogSchema = z.object({
	userId: z.string().nonempty(),
	worksiteId: z.string().nonempty(),
	type: z.enum([
		AttendanceType.CHECK_IN,
		AttendanceType.CHECK_OUT,
		AttendanceType.BREAK,
		AttendanceType.RETURN,
	]),
	logDate: z.iso.date(),
	logTime: z.iso.time(),
	status: z.enum([
		AttendanceStatus.ON_TIME,
		AttendanceStatus.EARLY,
		AttendanceStatus.LATE,
	]),
	locationLat: z.coerce.number().min(-90).max(90).meta({
		example: -6.2088,
		description: "Latitude coordinate of attendance location",
	}),
	locationLong: z.coerce.number().min(-180).max(180),
	photo: z
		.instanceof(File, { error: "Wajib memilih file gambar" })
		.refine((file) => file.size <= 5 * 1024 * 1024, {
			message: "Ukuran file maks. 5MB",
		})
		.refine(
			(file) => ["image/png", "image/jpeg", "image/webp"].includes(file.type),
			{
				message: "Hanya gambar JPG, PNG atau WebP yang diperbolehkan",
			},
		),
});
export type CreateAttendanceLogSchema = z.infer<
	typeof createAttendanceLogSchema
>;
