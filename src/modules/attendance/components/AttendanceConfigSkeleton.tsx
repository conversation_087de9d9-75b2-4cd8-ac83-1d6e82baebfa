import { Box, Grid, Skeleton, Stack, Typography } from "@mui/material";

const FormGroupSkeleton: React.FC<{ label: string }> = ({ label }) => (
	<Grid size={{ xs: 12, md: 6 }}>
		<Typography
			variant="h6"
			sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
		>
			{label}
		</Typography>
		<Stack direction="column" spacing={2}>
			<Box>
				<Typography variant="subtitle1">
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
			</Box>
			<Box>
				<Typography variant="subtitle1">
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
			</Box>
			<Box>
				<Typography variant="subtitle1">
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
			</Box>
		</Stack>
	</Grid>
);

export const AttendanceConfigSkeleton: React.FC = () => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Konfigurasi Absensi
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
				}}
			>
				<Typography
					variant="h6"
					sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
				>
					Pilih Lokasi
				</Typography>
				<Stack direction="column" spacing={1} sx={{ mb: 4 }}>
					<Skeleton variant="text" width="40%" height={25} />
					<Skeleton variant="rounded" height={40} />
				</Stack>

				<Grid container spacing={4}>
					<FormGroupSkeleton label="Waktu Check In" />
					<FormGroupSkeleton label="Waktu Check Out" />
					<FormGroupSkeleton label="Waktu Istirahat" />
					<FormGroupSkeleton label="Waktu Kembali" />
				</Grid>

				<Stack direction="row" justifyContent="flex-end" mt={4} spacing={2}>
					<Skeleton variant="rounded" width={120} height={36} />
				</Stack>
			</Box>
		</Box>
	);
};
