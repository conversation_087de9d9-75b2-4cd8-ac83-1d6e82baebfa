import { <PERSON>, Button, Grid, <PERSON>ack, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { DateInput } from "@/shared/components/common/Form/DateInput";
import { FileInput } from "@/shared/components/common/Form/FileInput";
import { SelectInput } from "@/shared/components/common/Form/SelectInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { TimeInput } from "@/shared/components/common/Form/TimeInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";

export const AttendanceLogFormContent: React.FC<{
	heading: string;
	isLoading: boolean;
	isSubmitting: boolean;
	userOptions: SelectOption[];
	worksiteOptions: SelectOption[];
	typeOptions: SelectOption[];
	statusOptions: SelectOption[];
}> = ({
	heading,
	isLoading,
	isSubmitting,
	userOptions,
	worksiteOptions,
	typeOptions,
	statusOptions,
}) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{heading}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="userId"
								label="User"
								placeholder="Cari dan pilih opsi"
								options={userOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="worksiteId"
								label="Lokasi Kerja"
								placeholder="Cari dan pilih opsi"
								options={worksiteOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<SelectInput
								name="type"
								label="Tipe"
								placeholder="Pilih opsi"
								options={typeOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<DateInput name="logDate" label="Tanggal" isLoading={isLoading} />
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TimeInput name="logTime" label="Jam" isLoading={isLoading} />
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<SelectInput
								name="status"
								label="Status"
								placeholder="Pilih opsi"
								options={statusOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="locationLat"
								label="Latitude"
								type="number"
								placeholder="Masukkan latitude"
								isLoading={isLoading}
								inputProps={{
									min: -90,
									max: 90,
									step: 0.0001,
								}}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="locationLong"
								label="Longitude"
								type="number"
								placeholder="Masukkan longitude"
								isLoading={isLoading}
								inputProps={{
									min: -180,
									max: 180,
									step: 0.0001,
								}}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<FileInput
								name="photo"
								label="Photo"
								isLoading={isLoading}
								accept="image/*"
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/attendance"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
