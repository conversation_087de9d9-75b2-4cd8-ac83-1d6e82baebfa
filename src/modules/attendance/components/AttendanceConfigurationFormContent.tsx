import { <PERSON>, Button, Grid, <PERSON>ack, Typography } from "@mui/material";
import { useFormContext } from "react-hook-form";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { MapField } from "@/shared/components/common/Form/MapField";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { TimeInput } from "@/shared/components/common/Form/TimeInput";
import { INDONESIA_TIMEZONES } from "../validation/updateAttendanceRuleSchema";

const timezoneOptions = INDONESIA_TIMEZONES.map((value) => ({
	label: value,
	value,
}));

export const AttendanceConfigurationFormContent: React.FC<{
	isSubmitting: boolean;
}> = ({ isSubmitting }) => {
	const { watch, setValue } = useFormContext();
	const lat = watch("latitude");
	const lng = watch("longitude");
	const radius = watch("radiusInMeter");

	return (
		<Box>
			<Grid container spacing={4}>
				<Grid size={{ xs: 12, md: 6 }}>
					<Typography
						variant="h6"
						sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
					>
						Waktu Check In
					</Typography>
					<Stack direction="column" spacing={2}>
						<TimeInput label="Waktu mulai" name="checkInStartTime" />
						<TimeInput label="Waktu akhir" name="checkInEndTime" />
						<TextInput
							type="number"
							label="Toleransi menit"
							name="checkInToleranceMinutes"
							placeholder="Toleransi menit"
						/>
					</Stack>
				</Grid>
				<Grid size={{ xs: 12, md: 6 }}>
					<Typography
						variant="h6"
						sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
					>
						Waktu Check Out
					</Typography>
					<Stack direction="column" spacing={2}>
						<TimeInput label="Waktu mulai" name="checkOutStartTime" />
						<TimeInput label="Waktu akhir" name="checkOutEndTime" />
						<TextInput
							type="number"
							label="Toleransi menit"
							name="checkOutToleranceMinutes"
							placeholder="Toleransi menit"
						/>
					</Stack>
				</Grid>
				<Grid size={{ xs: 12, md: 6 }}>
					<Typography
						variant="h6"
						sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
					>
						Waktu Istirahat
					</Typography>
					<Stack direction="column" spacing={2}>
						<TimeInput label="Waktu mulai" name="breakStartTime" />
						<TimeInput label="Waktu akhir" name="breakEndTime" />
						<TextInput
							type="number"
							label="Toleransi menit"
							name="breakToleranceMinutes"
							placeholder="Toleransi menit"
						/>
					</Stack>
				</Grid>
				<Grid size={{ xs: 12, md: 6 }}>
					<Typography
						variant="h6"
						sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
					>
						Waktu Kembali
					</Typography>
					<Stack direction="column" spacing={2}>
						<TimeInput label="Waktu mulai" name="returnStartTime" />
						<TimeInput label="Waktu akhir" name="returnEndTime" />
						<TextInput
							type="number"
							label="Toleransi menit"
							name="returnToleranceMinutes"
							placeholder="Toleransi menit"
						/>
					</Stack>
				</Grid>
			</Grid>

			<Box sx={{ mt: 4 }}>
				<Typography
					variant="h6"
					sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
				>
					Lokasi Absensi
				</Typography>
				<Grid container spacing={4}>
					<Grid size={{ xs: 12, md: 6 }}>
						<Stack direction="column" spacing={2}>
							<AutocompleteSelectInput
								options={timezoneOptions}
								label="Timezone"
								name="timezone"
							/>
							<TextInput
								type="number"
								label="Radius (m)"
								name="radiusInMeter"
							/>
							<TextInput type="number" label="Latitude" name="latitude" />
							<TextInput type="number" label="Longitude" name="longitude" />
						</Stack>
					</Grid>
					<Grid size={{ xs: 12, md: 6 }}>
						<Box sx={{ pt: { xs: 0, md: 4 } }}>
							<MapField
								latitude={lat}
								longitude={lng}
								setLatitude={(val) => setValue("latitude", val)}
								setLongitude={(val) => setValue("longitude", val)}
								radius={radius}
							/>
						</Box>
					</Grid>
				</Grid>
			</Box>

			<Stack
				direction="row"
				spacing={2}
				alignItems="center"
				justifyContent="end"
				sx={{ mt: 4 }}
			>
				<Button
					type="submit"
					variant="contained"
					color="primary"
					sx={{ px: 3 }}
					loading={isSubmitting}
					loadingPosition="start"
				>
					Simpan
				</Button>
			</Stack>
		</Box>
	);
};
