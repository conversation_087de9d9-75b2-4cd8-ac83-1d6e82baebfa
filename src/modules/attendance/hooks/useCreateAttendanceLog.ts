import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateAttendanceLogResponse,
} from "@/shared/types/api";
import { createAttendanceLog } from "../api/createAttendanceLog";

interface UseCreateAttendanceLogOptions {
	onSuccessCallback?: (data: CreateAttendanceLogResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateAttendanceLog = (
	options?: UseCreateAttendanceLogOptions,
) => {
	return useMutation<
		CreateAttendanceLogResponse,
		AxiosError<BaseErrorResponse>,
		FormData
	>({
		mutationFn: createAttendanceLog,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
