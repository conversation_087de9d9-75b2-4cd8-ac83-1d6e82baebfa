import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateAttendanceLogResponse,
} from "@/shared/types/api";
import { updateAttendanceLog } from "../api/updateAttendanceLog";

interface UseUpdateAttendanceLogOptions {
	onSuccessCallback?: (data: UpdateAttendanceLogResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateAttendanceLog = (
	options?: UseUpdateAttendanceLogOptions,
) => {
	return useMutation<
		UpdateAttendanceLogResponse,
		AxiosError<BaseErrorResponse>,
		{ attendanceLogId: string; payload: FormData }
	>({
		mutationFn: ({ attendanceLogId, payload }) => {
			return updateAttendanceLog(attendanceLogId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
