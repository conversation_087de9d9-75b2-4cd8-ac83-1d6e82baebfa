import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateAttendanceRulePayload,
	UpdateAttendanceRuleResponse,
} from "@/shared/types/api";
import { updateAttendanceRule } from "../api/updateAttendanceRule";

interface UseUpdateAttendanceRuleOptions {
	onSuccessCallback?: (data: UpdateAttendanceRuleResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateAttendanceRule = (
	options?: UseUpdateAttendanceRuleOptions,
) => {
	return useMutation<
		UpdateAttendanceRuleResponse,
		AxiosError<BaseErrorResponse>,
		{ id: string; payload: UpdateAttendanceRulePayload }
	>({
		mutationFn: ({ id, payload }) => {
			return updateAttendanceRule(id, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
