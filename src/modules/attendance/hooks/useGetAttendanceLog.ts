import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosResponse } from "axios";
import type {
	BaseErrorResponse,
	GetAttendanceLogResponse,
} from "@/shared/types/api";
import { getAttendanceLog } from "../api/getAttendanceLog";

export const useGetAttendanceLog = (
	attendanceLogId: string,
	options?: Omit<
		UseQueryOptions<GetAttendanceLogResponse, AxiosResponse<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getAttendanceLog", attendanceLogId],
		queryFn: async () => {
			return getAttendanceLog(attendanceLogId);
		},
		...options,
	});
};
