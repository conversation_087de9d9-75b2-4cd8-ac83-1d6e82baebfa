import { apiClient } from "@/shared/api/apiClient";
import type {
	UpdateAttendanceRulePayload,
	UpdateAttendanceRuleResponse,
} from "@/shared/types/api";

export const updateAttendanceRule = async (
	attendanceRuleId: string,
	payload: UpdateAttendanceRulePayload,
) => {
	const result = await apiClient.put<UpdateAttendanceRuleResponse>(
		`/api/v1/admin/attendance-rules/${attendanceRuleId}`,
		payload,
	);
	return result;
};
