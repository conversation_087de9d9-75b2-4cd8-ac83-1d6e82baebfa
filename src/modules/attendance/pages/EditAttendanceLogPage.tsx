import { useParams } from "react-router";
import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { useGetWorksiteOptions } from "@/modules/worksite/hooks/useGetWorksiteOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { AttendanceLogFormContent } from "../components/AttendanceLogFormContent";
import {
	AttendanceStatus,
	AttendanceStatusLabel,
	AttendanceType,
	AttendanceTypeLabel,
} from "../constants";
import { useGetAttendanceLog } from "../hooks/useGetAttendanceLog";
import { useUpdateAttendanceLog } from "../hooks/useUpdateAttendanceLog";
import {
	type UpdateAttendanceLogSchema,
	updateAttendanceLogSchema,
} from "../validation/updateAttendanceLogSchema";

const typeOptions: SelectOption[] = Object.entries(AttendanceType).map(
	([key, value]) => ({
		label: AttendanceTypeLabel[key as keyof typeof AttendanceType],
		value,
	}),
);

const statusOptions: SelectOption[] = Object.entries(AttendanceStatus).map(
	([key, value]) => ({
		label: AttendanceStatusLabel[key as keyof typeof AttendanceStatus],
		value,
	}),
);

const EditAttendanceLogPage: React.FC = () => {
	const attendanceLogId = useParams()?.attendanceLogId as string;

	const { data: attendanceLogData, isLoading: isAttendanceLogLoading } =
		useGetAttendanceLog(attendanceLogId);
	const { data: userOptionData, isLoading: isUserLoading } =
		useGetUserOptions();
	const { data: worksiteOptionsData, isLoading: isWorksiteLoading } =
		useGetWorksiteOptions();

	const isLoading =
		isUserLoading || isWorksiteLoading || isAttendanceLogLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateAttendanceLog({
		onSuccessCallback: (data) => handleSuccess(data.message, "/attendance"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateAttendanceLogSchema) => {
		const formData = new FormData();

		if (data.userId) formData.append("userId", data.userId);
		if (data.worksiteId) formData.append("worksiteId", data.worksiteId);
		if (data.type) formData.append("type", data.type);
		if (data.logDate) formData.append("logDate", data.logDate);
		if (data.logTime) formData.append("logTime", data.logTime);
		if (data.status) formData.append("status", data.status);
		if (data.locationLat)
			formData.append("locationLat", String(data.locationLat));
		if (data.locationLong)
			formData.append("locationLong", String(data.locationLong));
		if (data.photo) formData.append("photo", data.photo);

		mutation.mutate({ attendanceLogId, payload: formData });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !attendanceLogData?.data) {
		return <NotFoundPage resourceType="Kehadiran" redirectTo="/attendance" />;
	}

	const defaultValues: UpdateAttendanceLogSchema = {
		userId: attendanceLogData?.data.userId,
		worksiteId: attendanceLogData?.data.worksiteId,
		type: attendanceLogData?.data.type,
		status: attendanceLogData?.data.status,
		logDate: attendanceLogData?.data.logDate,
		logTime: attendanceLogData?.data.logTime,
		locationLat: attendanceLogData?.data.locationLat,
		locationLong: attendanceLogData?.data.locationLong,
	};

	return (
		<FormProvider
			schema={updateAttendanceLogSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<AttendanceLogFormContent
				heading="Edit Attendance"
				isLoading={false}
				isSubmitting={mutation.isPending}
				userOptions={userOptionData?.data as SelectOption[]}
				worksiteOptions={worksiteOptionsData?.data as SelectOption[]}
				typeOptions={typeOptions}
				statusOptions={statusOptions}
			/>
		</FormProvider>
	);
};

export default EditAttendanceLogPage;
