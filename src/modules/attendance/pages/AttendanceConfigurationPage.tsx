import {
	Autocomplete,
	Box,
	FormControl,
	TextField,
	Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { getWorksiteOptions } from "@/modules/worksite/api/getWorksiteOptions";
import { Footer } from "@/shared/components/common/Footer";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import type { SelectOption } from "@/shared/types/common";
import { getAttendanceRuleByWorksite } from "../api/getAttendanceRuleByWorksite";
import { AttendanceConfigSkeleton } from "../components/AttendanceConfigSkeleton";
import { AttendanceConfigurationFormContent } from "../components/AttendanceConfigurationFormContent";
import { useUpdateAttendanceRule } from "../hooks/useUpdateAttendanceRule";
import {
	type UpdateAttendanceRuleSchema,
	updateAttendanceRuleSchema,
} from "../validation/updateAttendanceRuleSchema";

const AttendanceConfigurationPage: React.FC = () => {
	const [worksiteOptions, setWorksiteOptions] = useState<SelectOption[]>([]);
	const [selectedWorksite, setSelectedWorksite] = useState<SelectOption | null>(
		null,
	);
	const [defaultValues, setDefaultValues] =
		useState<UpdateAttendanceRuleSchema | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [attendanceRuleId, setAttendanceRuleId] = useState<string | null>(null);

	const mutation = useUpdateAttendanceRule();
	const onSubmit = (data: UpdateAttendanceRuleSchema) => {
		if (attendanceRuleId) {
			mutation.mutate({ id: attendanceRuleId, payload: data });
		}
	};

	useEffect(() => {
		const fetchWorksites = async () => {
			const result = await getWorksiteOptions();
			setWorksiteOptions(result.data);

			if (result.data.length > 0) {
				setSelectedWorksite(result.data[0]);
			}

			setIsLoading(false);
		};

		fetchWorksites();
	}, []);

	useEffect(() => {
		setIsLoading(true);
		if (selectedWorksite) {
			const fetchAttendanceRule = async () => {
				const result = await getAttendanceRuleByWorksite(
					selectedWorksite.value as string,
				);
				setDefaultValues(result.data as UpdateAttendanceRuleSchema);
				setAttendanceRuleId(result.data.id);
				setIsLoading(false);
			};

			fetchAttendanceRule();
		}
	}, [selectedWorksite]);

	if (isLoading) return <AttendanceConfigSkeleton />;

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Konfigurasi Absensi
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
				}}
			>
				<Typography
					variant="h6"
					sx={{ mb: 1, fontWeight: 700, fontSize: "1.25rem" }}
				>
					Pilih Lokasi
				</Typography>
				<FormControl fullWidth size="small" sx={{ mb: 4 }}>
					<Typography
						component="label"
						variant="subtitle1"
						htmlFor="worksite"
						mb={1}
					>
						Lokasi Kerja
					</Typography>
					<Autocomplete
						options={worksiteOptions}
						value={selectedWorksite}
						onChange={(_, newVal) => {
							if (newVal) setSelectedWorksite(newVal);
						}}
						isOptionEqualToValue={(opt, val) => opt.value === val.value}
						renderInput={(params) => (
							<TextField
								{...params}
								placeholder="Pilih lokasi kerja"
								size="small"
							/>
						)}
					/>
				</FormControl>

				{selectedWorksite && defaultValues ? (
					<FormProvider
						schema={updateAttendanceRuleSchema}
						defaultValues={defaultValues}
						onSubmit={onSubmit}
					>
						<AttendanceConfigurationFormContent
							isSubmitting={mutation.isPending}
						/>
					</FormProvider>
				) : null}
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default AttendanceConfigurationPage;
