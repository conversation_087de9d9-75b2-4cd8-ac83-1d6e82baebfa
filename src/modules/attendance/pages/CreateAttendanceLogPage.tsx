import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { useGetWorksiteOptions } from "@/modules/worksite/hooks/useGetWorksiteOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { AttendanceLogFormContent } from "../components/AttendanceLogFormContent";
import {
	AttendanceStatus,
	AttendanceStatusLabel,
	AttendanceType,
	AttendanceTypeLabel,
} from "../constants";
import { useCreateAttendanceLog } from "../hooks/useCreateAttendanceLog";
import {
	type CreateAttendanceLogSchema,
	createAttendanceLogSchema,
} from "../validation/createAttendanceLogSchema";

const typeOptions: SelectOption[] = Object.entries(AttendanceType).map(
	([key, value]) => ({
		label: AttendanceTypeLabel[key as keyof typeof AttendanceType],
		value,
	}),
);

const statusOptions: SelectOption[] = Object.entries(AttendanceStatus).map(
	([key, value]) => ({
		label: AttendanceStatusLabel[key as keyof typeof AttendanceStatus],
		value,
	}),
);

const CreateAttendanceLogPage: React.FC = () => {
	const { data: userOptionData, isLoading: isUserLoading } =
		useGetUserOptions();
	const { data: worksiteOptionsData, isLoading: isWorksiteLoading } =
		useGetWorksiteOptions();

	const isLoading = isUserLoading || isWorksiteLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateAttendanceLog({
		onSuccessCallback: (data) => handleSuccess(data.message, "/attendance"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateAttendanceLogSchema) => {
		const formData = new FormData();

		formData.append("userId", data.userId);
		formData.append("worksiteId", data.worksiteId);
		formData.append("type", data.type);
		formData.append("logDate", data.logDate);
		formData.append("logTime", data.logTime);
		formData.append("status", data.status);
		formData.append("locationLat", String(data.locationLat));
		formData.append("locationLong", String(data.locationLong));
		formData.append("photo", data.photo);

		mutation.mutate(formData);
	};

	return (
		<FormProvider
			schema={createAttendanceLogSchema}
			defaultValues={{}}
			onSubmit={onSubmit}
		>
			<AttendanceLogFormContent
				heading="Tambah Attendance"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				userOptions={userOptionData?.data as SelectOption[]}
				worksiteOptions={worksiteOptionsData?.data as SelectOption[]}
				typeOptions={typeOptions}
				statusOptions={statusOptions}
			/>
		</FormProvider>
	);
};

export default CreateAttendanceLogPage;
