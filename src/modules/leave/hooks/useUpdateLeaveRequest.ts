import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateLeaveRequestResponse,
} from "@/shared/types/api";
import { updateLeaveRequest } from "../api/updateLeaveRequest";

interface UseUpdateLeaveRequestOptions {
	onSuccessCallback?: (data: UpdateLeaveRequestResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateLeaveRequest = (
	options?: UseUpdateLeaveRequestOptions,
) => {
	return useMutation<
		UpdateLeaveRequestResponse,
		AxiosError<BaseErrorResponse>,
		{ leaveRequestId: string; payload: FormData }
	>({
		mutationFn: ({ leaveRequestId, payload }) => {
			return updateLeaveRequest(leaveRequestId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
