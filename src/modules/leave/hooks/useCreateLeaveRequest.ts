import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateLeaveRequestResponse,
} from "@/shared/types/api";
import { createLeaveRequest } from "../api/createLeaveRequest";

interface UseCreateLeaveRequestOptions {
	onSuccessCallback?: (data: CreateLeaveRequestResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateLeaveRequest = (options?: UseCreateLeaveRequestOptions) => {
	return useMutation<
		CreateLeaveRequestResponse,
		AxiosError<BaseErrorResponse>,
		FormData
	>({
		mutationFn: createLeaveRequest,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
