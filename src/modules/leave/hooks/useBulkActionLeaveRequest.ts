import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	BulkActionPayload,
	BulkActionResponse,
} from "@/shared/types/api";
import { bulkActionLeaveRequest } from "../api/bulkActionLeaveRequest";

interface UseBulkActionLeaveRequestOptions {
	onSuccessCallback?: (data: BulkActionResponse | Blob) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useBulkActionLeaveRequest = (
	options?: UseBulkActionLeaveRequestOptions,
) => {
	return useMutation<
		BulkActionResponse | Blob,
		AxiosError<BaseErrorResponse>,
		BulkActionPayload
	>({
		mutationFn: bulkActionLeaveRequest,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
