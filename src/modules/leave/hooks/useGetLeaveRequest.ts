import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetLeaveRequestResponse,
} from "@/shared/types/api";
import { getLeaveRequest } from "../api/getLeaveRequest";

export const useGetLeaveRequest = (
	leaveRequestId: string,
	options?: Omit<
		UseQueryOptions<GetLeaveRequestResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getLeaveRequest", leaveRequestId],
		queryFn: async () => {
			return getLeaveRequest(leaveRequestId);
		},
		...options,
	});
};
