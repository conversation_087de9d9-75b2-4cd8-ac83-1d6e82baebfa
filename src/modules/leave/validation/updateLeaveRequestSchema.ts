import { z } from "zod";

export const updateLeaveRequestSchema = z.object({
	userId: z.string().optional(),
	leavePolicyId: z.string().optional(),
	startDate: z.string().optional(),
	endDate: z.string().optional(),
	description: z.string().optional(),
	document: z
		.instanceof(File)
		.refine((file) => file.size <= 10 * 1024 * 1024, {
			message: "Ukuran file maks. 10MB",
		})
		.refine(
			(file) => [
				"application/pdf",
				"image/png", 
				"image/jpeg", 
				"image/webp",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document"
			].includes(file.type),
			{
				message: "Hanya file PDF, gambar (JPG, PNG, WebP), atau dokumen Word yang diperbolehkan",
			},
		)
		.optional(),
});

export type UpdateLeaveRequestSchema = z.infer<typeof updateLeaveRequestSchema>;
