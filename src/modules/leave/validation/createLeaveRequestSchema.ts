import { z } from "zod";

export const createLeaveRequestSchema = z.object({
	userId: z.string().min(1, { message: "User wajib dipilih" }),
	leavePolicyId: z.string().min(1, { message: "Leave policy wajib dipilih" }),
	startDate: z.string().min(1, { message: "Tanggal mulai wajib diisi" }),
	endDate: z.string().min(1, { message: "Tanggal selesai wajib diisi" }),
	description: z.string().min(1, { message: "Deskripsi wajib diisi" }),
	document: z
		.instanceof(File, { message: "File dokumen wajib dipilih" })
		.refine((file) => file.size <= 10 * 1024 * 1024, {
			message: "Ukuran file maks. 10MB",
		})
		.refine(
			(file) => [
				"application/pdf",
				"image/png", 
				"image/jpeg", 
				"image/webp",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document"
			].includes(file.type),
			{
				message: "Hanya file PDF, gambar (JPG, PNG, WebP), atau dokumen Word yang diperbolehkan",
			},
		)
		.optional(),
});

export type CreateLeaveRequestSchema = z.infer<typeof createLeaveRequestSchema>;
