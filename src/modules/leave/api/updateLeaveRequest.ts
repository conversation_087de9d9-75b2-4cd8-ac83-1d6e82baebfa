import { apiClient } from "@/shared/api/apiClient";
import type { UpdateLeaveRequestResponse } from "@/shared/types/api";

export const updateLeaveRequest = async (
	leaveRequestId: string,
	payload: FormData,
) => {
	const result = await apiClient.put<UpdateLeaveRequestResponse>(
		`/api/v1/admin/leave-requests/${leaveRequestId}`,
		payload,
		{
			headers: {
				"Content-Type": "multipart/form-data",
			},
		},
	);
	return result;
};
