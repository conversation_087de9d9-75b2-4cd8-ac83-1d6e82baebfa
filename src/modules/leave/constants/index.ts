import type { SelectOption } from "@/shared/types/common";

export const LeaveRequestStatus = {
	PENDING: "PENDING",
	APPROVED: "APPROVED",
	REJECTED: "REJECTED",
} as const;

export const LeaveRequestStatusLabel: Record<
	keyof typeof LeaveRequestStatus,
	string
> = {
	PENDING: "Menunggu Persetujuan",
	APPROVED: "Disetujui",
	REJECTED: "Ditolak",
};

export const leaveRequestStatusSelectOption: SelectOption[] = [
	{
		label: "Menunggu Persetujuan",
		value: "PENDING",
	},
	{
		label: "Disetujui",
		value: "APPROVED",
	},
	{
		label: "<PERSON><PERSON><PERSON>",
		value: "REJECTED",
	},
];
