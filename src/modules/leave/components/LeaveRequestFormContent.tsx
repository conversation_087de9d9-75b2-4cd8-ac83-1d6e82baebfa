import { <PERSON>, Button, Grid, <PERSON>ack, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { DateInput } from "@/shared/components/common/Form/DateInput";
import { FileInput } from "@/shared/components/common/Form/FileInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";

interface LeaveRequestFormContentProps {
	label: string;
	isLoading: boolean;
	isSubmitting: boolean;
	userOptions: SelectOption[];
	leavePolicyOptions: SelectOption[];
}

export const LeaveRequestFormContent: React.FC<
	LeaveRequestFormContentProps
> = ({ label, isLoading, isSubmitting, userOptions, leavePolicyOptions }) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="userId"
								label="User"
								placeholder="Cari dan pilih opsi"
								options={userOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="leavePolicyId"
								label="Kebijakan Cuti"
								placeholder="Cari dan pilih opsi"
								options={leavePolicyOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<DateInput
								name="startDate"
								label="Tanggal Mulai"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<DateInput
								name="endDate"
								label="Tanggal Selesai"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 8 }}>
							<TextInput
								name="description"
								label="Deskripsi"
								placeholder="Masukkan deskripsi cuti"
								multiline
								rows={4}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 4 }}>
							<FileInput
								name="document"
								label="Dokumen Pendukung"
								isLoading={isLoading}
								accept=".pdf,.doc,.docx,.png,.jpg,.jpeg,.webp"
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/leaves"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
