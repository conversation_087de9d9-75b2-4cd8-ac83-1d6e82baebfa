import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetTaskAssignableUsersResponse,
} from "@/shared/types/api";
import { getTaskAssignableUsers } from "../api/getTaskAssignableUsers";

export const useGetTaskAssignableUsers = (
	userId: string,
	options?: Omit<
		UseQueryOptions<
			GetTaskAssignableUsersResponse,
			AxiosError<BaseErrorResponse>
		>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getTaskAssignableUsers", userId],
		queryFn: () => {
			return getTaskAssignableUsers(userId);
		},
		...options,
	});
};
