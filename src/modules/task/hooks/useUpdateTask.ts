import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateTaskResponse,
} from "@/shared/types/api";
import { updateTask } from "../api/updateTask";

interface UseUpdateTaskOptions {
	onSuccessCallback?: (data: UpdateTaskResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateTask = (options?: UseUpdateTaskOptions) => {
	return useMutation<
		UpdateTaskResponse,
		AxiosError<BaseErrorResponse>,
		{ taskId: string; payload: FormData }
	>({
		mutationFn: ({ taskId, payload }) => updateTask(taskId, payload),
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
