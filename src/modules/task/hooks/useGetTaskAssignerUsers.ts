import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetTaskAssignerUsersResponse,
} from "@/shared/types/api";
import { getTaskAssignerUsers } from "../api/getTaskAssignerUsers";

export const useGetTaskAssignerUsers = (
	options?: Omit<
		UseQueryOptions<
			GetTaskAssignerUsersResponse,
			AxiosError<BaseErrorResponse>
		>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getTaskAssignerUsers"],
		queryFn: () => {
			return getTaskAssignerUsers();
		},
		...options,
	});
};
