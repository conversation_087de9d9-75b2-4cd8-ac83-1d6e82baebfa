import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateTaskResponse,
} from "@/shared/types/api";
import { createTask } from "../api/createTask";

interface UseCreateTaskOptions {
	onSuccessCallback?: (data: CreateTaskResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateTask = (options?: UseCreateTaskOptions) => {
	return useMutation<
		CreateTaskResponse,
		AxiosError<BaseErrorResponse>,
		FormData
	>({
		mutationFn: createTask,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
