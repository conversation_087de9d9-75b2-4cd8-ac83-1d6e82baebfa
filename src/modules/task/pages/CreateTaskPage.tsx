import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { TaskFormContent } from "../components/TaskFormContent";
import { useCreateTask } from "../hooks/useCreateTask";
import { useGetTaskAssignerUsers } from "../hooks/useGetTaskAssignerUsers";
import {
	type CreateTaskSchema,
	createTaskSchema,
} from "../validation/createTaskSchema";

const CreateTaskPage: React.FC = () => {
	const { data: assignerUsersData, isLoading: isAssignerUsersLoading } =
		useGetTaskAssignerUsers();

	const isLoading = isAssignerUsersLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateTask({
		onSuccessCallback: (data) => handleSuccess(data.message, "/tasks"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateTaskSchema) => {
		const formData = new FormData();
		formData.append("name", data.name);
		formData.append("description", data.description);
		formData.append("deadline", data.deadline);
		formData.append("assignerId", data.assignerId);

		// Handle multiple assignees
		data.assigneeIds.forEach((assigneeId) => {
			formData.append("assigneeIds", assigneeId);
		});

		// Handle multiple documents
		if (data.documents && data.documents.length > 0) {
			data.documents.forEach((document) => {
				formData.append("documents", document);
			});
		}

		// Handle multiple images
		if (data.images && data.images.length > 0) {
			data.images.forEach((image) => {
				formData.append("images", image);
			});
		}

		mutation.mutate(formData);
	};

	return (
		<FormProvider
			schema={createTaskSchema}
			defaultValues={{
				name: "",
				description: "",
				deadline: "",
				assignerId: "",
				assigneeIds: [],
				documents: undefined,
				images: undefined,
			}}
			onSubmit={onSubmit}
		>
			<TaskFormContent
				label="Buat Tugas Baru"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				assignerUserOptions={assignerUsersData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateTaskPage;
