import { <PERSON>, <PERSON>ton, Grid, Stack, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { DateInput } from "@/shared/components/common/Form/DateInput";
import { FileInput } from "@/shared/components/common/Form/FileInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";
import { useGetTaskAssignableUsers } from "../hooks/useGetTaskAssignableUsers";

interface TaskFormContentProps {
	label: string;
	isLoading: boolean;
	isSubmitting: boolean;
	assignerUserOptions: SelectOption[];
}

export const TaskFormContent: React.FC<TaskFormContentProps> = ({
	label,
	isLoading,
	isSubmitting,
	assignerUserOptions,
}) => {
	const { watch, setValue } = useFormContext();
	const assignerId = watch("assignerId");

	// Track initial assignerId untuk detect changes
	const [initialAssignerId, setInitialAssignerId] = useState<string | null>(
		null,
	);

	const { data: assignableUsersData, isLoading: isAssignableUsersLoading } =
		useGetTaskAssignableUsers(assignerId, {
			enabled: !!assignerId,
		});

	// Set initial assignerId on first render
	useEffect(() => {
		if (initialAssignerId === null && assignerId) {
			setInitialAssignerId(assignerId);
		}
	}, [initialAssignerId, assignerId]);

	// Reset assigneeIds ketika assignerId berubah (bukan initial load)
	useEffect(() => {
		if (initialAssignerId && assignerId && initialAssignerId !== assignerId) {
			setValue("assigneeIds", null);
		}
	}, [setValue, assignerId, initialAssignerId]);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="name"
								label="Nama Tugas"
								placeholder="Masukkan nama tugas"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="assignerId"
								label="Assigner"
								placeholder="Cari dan pilih assigner"
								options={assignerUserOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="assigneeIds"
								label="Assignee"
								placeholder="Cari dan pilih assignee"
								options={(assignableUsersData?.data as SelectOption[]) ?? []}
								isLoading={isLoading || isAssignableUsersLoading}
								multiple
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<DateInput
								name="deadline"
								label="Deadline"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 8 }}>
							<TextInput
								name="description"
								label="Deskripsi"
								placeholder="Masukkan deskripsi tugas"
								multiline
								rows={4}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<FileInput
								name="documents"
								label="Dokumen Pendukung"
								isLoading={isLoading}
								accept=".pdf,.doc,.docx"
								multiple
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<FileInput
								name="images"
								label="Gambar Pendukung"
								isLoading={isLoading}
								accept=".png,.jpg,.jpeg,.webp"
								multiple
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/tasks"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
