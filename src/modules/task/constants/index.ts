import type { SelectOption } from "@/shared/types/common";

export const TaskStatus = {
	PENDING: "PENDING",
	IN_PROGRESS: "IN_PROGRESS",
	IN_REVIEW: "IN_REVIEW",
	REVISION_REQUIRED: "REVISION_REQUIRED",
	COMPLETED: "COMPLETED",
	REJECTED: "REJECTED",
} as const;

export const TaskStatusLabel: Record<
	keyof typeof TaskStatus,
	string
> = {
	PENDING: "Menunggu",
	IN_PROGRESS: "Sedang Dikerjakan",
	IN_REVIEW: "Sedang Direview",
	REVISION_REQUIRED: "Perlu Revisi",
	COMPLETED: "Seles<PERSON>",
	REJECTED: "Ditolak",
};

export const taskStatusSelectOption: SelectOption[] = [
	{
		label: "Menunggu",
		value: "PENDING",
	},
	{
		label: "Sedang Dikerjakan",
		value: "IN_PROGRESS",
	},
	{
		label: "Sedang Direview",
		value: "IN_REVIEW",
	},
	{
		label: "Perlu Revisi",
		value: "REVISION_REQUIRED",
	},
	{
		label: "Selesai",
		value: "COMPLETED",
	},
	{
		label: "Ditolak",
		value: "REJECTED",
	},
];
