import { z } from "zod";

export const updateTaskSchema = z.object({
	name: z.string().optional(),
	description: z.string().optional(),
	deadline: z.string().optional(),
	assignerId: z.string().optional(),
	assigneeId: z.string().optional(),
	documents: z
		.array(z.instanceof(File))
		.max(5, { message: "Maksimal 5 file dokumen" })
		.refine(
			(files) => files.every((file) => file.size <= 10 * 1024 * 1024),
			{
				message: "Ukuran file maks. 10MB",
			},
		)
		.optional(),
	images: z
		.array(z.instanceof(File))
		.max(5, { message: "Maksimal 5 file gambar" })
		.refine(
			(files) => files.every((file) => file.size <= 10 * 1024 * 1024),
			{
				message: "Ukuran file maks. 10MB",
			},
		)
		.refine(
			(files) => files.every((file) => 
				["image/jpeg", "image/jpg", "image/png", "image/webp"].includes(file.type)
			),
			{
				message: "Hanya file gambar (JPEG, JPG, PNG, WebP) yang diperbolehkan",
			},
		)
		.optional(),
});

export type UpdateTaskSchema = z.infer<typeof updateTaskSchema>;
