import { z } from "zod";

export const createTaskSchema = z.object({
	name: z.string().min(1, { message: "Nama tugas wajib diisi" }),
	description: z.string().min(1, { message: "Deskripsi wajib diisi" }),
	deadline: z.string().min(1, { message: "Deadline wajib diisi" }),
	assignerId: z.string().min(1, { message: "Assigner wajib dipilih" }),
	assigneeIds: z.array(z.string()).min(1, { message: "Minimal satu assignee wajib dipilih" }),
	documents: z
		.array(z.instanceof(File))
		.max(5, { message: "Maksimal 5 file dokumen" })
		.refine(
			(files) => files.every((file) => file.size <= 10 * 1024 * 1024),
			{
				message: "Ukuran file maks. 10MB",
			},
		)
		.optional(),
	images: z
		.array(z.instanceof(File))
		.max(5, { message: "Maksimal 5 file gambar" })
		.refine(
			(files) => files.every((file) => file.size <= 10 * 1024 * 1024),
			{
				message: "Ukuran file maks. 10MB",
			},
		)
		.refine(
			(files) => files.every((file) => 
				["image/jpeg", "image/jpg", "image/png", "image/webp"].includes(file.type)
			),
			{
				message: "Hanya file gambar (JPEG, JPG, PNG, WebP) yang diperbolehkan",
			},
		)
		.optional(),
});

export type CreateTaskSchema = z.infer<typeof createTaskSchema>;
