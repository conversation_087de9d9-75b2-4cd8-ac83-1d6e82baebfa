import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetSupervisorOptionsResponse,
} from "@/shared/types/api";
import { getSupervisorOptions } from "../api/getSupervisorOptions";

export const useGetSupervisorOptions = (
	params: { roleId: string; excludeId?: string },
	options?: Omit<
		UseQueryOptions<
			GetSupervisorOptionsResponse,
			AxiosError<BaseErrorResponse>
		>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getSupervisorOptions", params],
		queryFn: () => {
			return getSupervisorOptions(params.roleId, params.excludeId);
		},
		...options,
	});
};
