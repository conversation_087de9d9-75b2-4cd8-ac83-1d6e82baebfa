import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateRolePayload,
	UpdateRoleResponse,
} from "@/shared/types/api";
import { updateRole } from "../api/updateRole";

interface UseUpdateRoleOptions {
	onSuccessCallback?: (data: UpdateRoleResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateRole = (options?: UseUpdateRoleOptions) => {
	return useMutation<
		UpdateRoleResponse,
		AxiosError<BaseErrorResponse>,
		{ roleId: string; payload: UpdateRolePayload }
	>({
		mutationFn: ({ roleId, payload }) => {
			return updateRole(roleId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
