import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type { BaseErrorResponse, GetRoleResponse } from "@/shared/types/api";
import { getRole } from "../api/getRole";

export const useGetRole = (
	roleId: string,
	options?: Omit<
		UseQueryOptions<GetRoleResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getRole", roleId],
		queryFn: () => {
			return getRole(roleId);
		},
		...options,
	});
};
