import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetRoleTreeResponse,
} from "@/shared/types/api";
import { getRoleTree } from "../api/getRoleTree";

export const useGetRoleTree = (
	options?: Omit<
		UseQueryOptions<GetRoleTreeResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		...options,
		queryKey: ["getRoleTree"],
		queryFn: getRoleTree,
	});
};
