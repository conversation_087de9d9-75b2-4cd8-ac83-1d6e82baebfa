import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetRoleOptionsResponse,
} from "@/shared/types/api";
import { getRoleOptions } from "../api/getRoleOptions";

export const useGetRoleOptions = (
	excludeId?: string,
	options?: Omit<
		UseQueryOptions<GetRoleOptionsResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getRoleOptions", excludeId],
		queryFn: () => {
			return getRoleOptions(excludeId);
		},
		...options,
	});
};
