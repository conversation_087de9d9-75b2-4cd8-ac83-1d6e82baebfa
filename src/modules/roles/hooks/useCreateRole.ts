import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateRolePayload,
	CreateRoleResponse,
} from "@/shared/types/api";
import { createRole } from "../api/createRole";

interface UseCreateRoleOptions {
	onSuccessCallback?: (data: CreateRoleResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateRole = (options?: UseCreateRoleOptions) => {
	return useMutation<
		CreateRoleResponse,
		AxiosError<BaseErrorResponse>,
		CreateRolePayload
	>({
		mutationFn: createRole,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
