import { useParams } from "react-router";
import { useGetRoleOptions } from "@/modules/roles/hooks/useGetRoleOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { RoleFormContent } from "../components/RoleFormContent";
import { useGetRole } from "../hooks/useGetRole";
import { useUpdateRole } from "../hooks/useUpdateRole";
import {
	type UpdateRoleSchema,
	updateRoleSchema,
} from "../validation/updateRoleSchema";

export const EditRolePage: React.FC = () => {
	const roleId = useParams()?.roleId as string;

	const { data: roleData, isLoading: isRoleLoading } = useGetRole(roleId);
	const { data: roleOptionsData, isLoading: isRoleOptionsLoading } =
		useGetRoleOptions(roleId);

	const isLoading = isRoleLoading || isRoleOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateRole({
		onSuccessCallback: (data) =>
			handleSuccess(data.message, "/organizations"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateRoleSchema) => {
		mutation.mutate({ roleId, payload: data });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !roleData?.data) {
		return (
			<NotFoundPage
				resourceType="Jabatan"
				redirectTo="/organizations"
			/>
		);
	}

	const defaultValues: UpdateRoleSchema = {
		name: roleData?.data.name || "",
		description: roleData?.data.description || undefined,
		parentId: roleData?.data.parentId || undefined,
	};

	return (
		<FormProvider
			schema={updateRoleSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<RoleFormContent
				isLoading={false}
				isSubmitting={mutation.isPending}
				roleOptions={roleOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditRolePage;
