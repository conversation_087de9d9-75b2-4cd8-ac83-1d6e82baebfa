import { useGetRoleOptions } from "@/modules/roles/hooks/useGetRoleOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { RoleFormContent } from "../components/RoleFormContent";
import { useCreateRole } from "../hooks/useCreateRole";
import {
	type CreateRoleSchema,
	createRoleSchema,
} from "../validation/createRoleSchema";

export const CreateRolePage: React.FC = () => {
	const { data: roleOptionsData, isLoading } = useGetRoleOptions();

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateRole({
		onSuccessCallback: (data) =>
			handleSuccess(data.message, "/organizations"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateRoleSchema) => {
		mutation.mutate(data);
	};

	return (
		<FormProvider
			schema={createRoleSchema}
			defaultValues={{
				name: "",
				description: undefined,
				parentId: undefined,
			}}
			onSubmit={onSubmit}
		>
			<RoleFormContent
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				roleOptions={roleOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateRolePage;
