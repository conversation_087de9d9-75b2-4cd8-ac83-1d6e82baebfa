import { Box, Typography } from "@mui/material";
import { Footer } from "@/shared/components/common/Footer";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { OrgChartTree } from "@/shared/components/common/OrgChartTree";
import type { OrgChartNode } from "@/shared/types/common";
import { useGetRoleTree } from "../hooks/useGetRoleTree";

const wrapRoleTree = (nodes: OrgChartNode[]) => {
	return [
		{
			name: "Organization",
			attributes: {},
			children: nodes,
		},
	];
};

const RoleTreePage: React.FC = () => {
	const { data: roleTreeData, isLoading } = useGetRoleTree();

	if (isLoading) return <FullPageLoader />;

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Hierarki Jabatan
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<OrgChartTree
					data={wrapRoleTree(roleTreeData?.data as OrgChartNode[])}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default RoleTreePage;
