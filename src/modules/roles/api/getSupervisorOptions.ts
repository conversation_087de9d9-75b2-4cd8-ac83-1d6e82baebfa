import { apiClient } from "@/shared/api/apiClient";
import type { GetSupervisorOptionsResponse } from "@/shared/types/api";

export const getSupervisorOptions = async (
	roleId: string,
	excludeId?: string,
) => {
	const result = await apiClient.get<GetSupervisorOptionsResponse>(
		`/api/v1/admin/roles/${roleId}/supervisors/options${excludeId ? `?excludeId=${excludeId}` : ""}`,
	);
	return result;
};
