import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateLeavePolicyPayload,
	UpdateLeavePolicyResponse,
} from "@/shared/types/api";
import { updateLeavePolicy } from "../api/updateLeavePolicy";

interface UseUpdateLeavePolicyOptions {
	onSuccessCallback?: (data: UpdateLeavePolicyResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateLeavePolicy = (options?: UseUpdateLeavePolicyOptions) => {
	return useMutation<
		UpdateLeavePolicyResponse,
		AxiosError<BaseErrorResponse>,
		{ leavePolicyId: string; payload: UpdateLeavePolicyPayload }
	>({
		mutationFn: ({ leavePolicyId, payload }) => {
			return updateLeavePolicy(leavePolicyId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
