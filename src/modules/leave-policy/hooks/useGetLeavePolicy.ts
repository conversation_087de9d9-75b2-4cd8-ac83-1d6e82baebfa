import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetLeavePolicyResponse,
} from "@/shared/types/api";
import { getLeavePolicy } from "../api/getLeavePolicy";

export const useGetLeavePolicy = (
	leavePolicyId: string,
	options?: Omit<
		UseQueryOptions<GetLeavePolicyResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getLeavePolicy", leavePolicyId],
		queryFn: async () => {
			return getLeavePolicy(leavePolicyId);
		},
		...options,
	});
};
