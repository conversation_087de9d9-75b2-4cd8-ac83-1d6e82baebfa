import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateLeavePolicyPayload,
	CreateLeavePolicyResponse,
} from "@/shared/types/api";
import { createLeavePolicy } from "../api/createLeavePolicy";

interface UseCreateLeavePolicyOptions {
	onSuccessCallback?: (data: CreateLeavePolicyResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateLeavePolicy = (options?: UseCreateLeavePolicyOptions) => {
	return useMutation<
		CreateLeavePolicyResponse,
		AxiosError<BaseErrorResponse>,
		CreateLeavePolicyPayload
	>({
		mutationFn: createLeavePolicy,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
