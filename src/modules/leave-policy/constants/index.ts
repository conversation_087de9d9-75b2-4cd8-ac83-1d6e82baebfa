import type { SelectOption } from "@/shared/types/common";

export const LeavePolicyStatus = {
	ACTIVE: "ACTIVE",
	INACTIVE: "INACTIVE",
} as const;

export const LeavePolicyStatusLabel: Record<
	keyof typeof LeavePolicyStatus,
	string
> = {
	ACTIVE: "Aktif",
	INACTIVE: "Tidak Aktif",
};

export const leavePolicyStatusSelectOption: SelectOption[] = [
	{
		label: "Aktif",
		value: "ACTIVE",
	},
	{
		label: "Tidak Aktif",
		value: "INACTIVE",
	},
];
