import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import { LeavePolicyFormContent } from "../components/LeavePolicyFormContent";
import { useCreateLeavePolicy } from "../hooks/useCreateLeavePolicy";
import {
	type CreateLeavePolicySchema,
	createLeavePolicySchema,
} from "../validation/createLeavePolicySchema";

const CreateLeavePolicyPage: React.FC = () => {
	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateLeavePolicy({
		onSuccessCallback: (data) => handleSuccess(data.message, "/leave-policies"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateLeavePolicySchema) => {
		mutation.mutate(data);
	};

	return (
		<FormProvider
			schema={createLeavePolicySchema}
			defaultValues={{
				name: "",
				description: "",
				quota: 0,
				isCountedAsPresent: "",
			}}
			onSubmit={onSubmit}
		>
			<LeavePolicyFormContent
				label="Buat Kebijakan Cuti Baru"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default CreateLeavePolicyPage;
