import { useParams } from "react-router";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import { LeavePolicyFormContent } from "../components/LeavePolicyFormContent";
import { useGetLeavePolicy } from "../hooks/useGetLeavePolicy";
import { useUpdateLeavePolicy } from "../hooks/useUpdateLeavePolicy";
import {
	type UpdateLeavePolicySchema,
	updateLeavePolicySchema,
} from "../validation/updateLeavePolicySchema";

const EditLeavePolicyPage: React.FC = () => {
	const leavePolicyId = useParams().leavePolicyId as string;

	const { data: leavePolicyData, isLoading: isLeavePolicyLoading } =
		useGetLeavePolicy(leavePolicyId);

	const isLoading = isLeavePolicyLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateLeavePolicy({
		onSuccessCallback: (data) => handleSuccess(data.message, "/leave-policies"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateLeavePolicySchema) => {
		mutation.mutate({ leavePolicyId, payload: data });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !leavePolicyData?.data) {
		return (
			<NotFoundPage
				resourceType="Kebijakan Cuti"
				redirectTo="/leave-policies"
			/>
		);
	}

	const defaultValues = {
		name: leavePolicyData?.data.name,
		description: leavePolicyData?.data.description || undefined,
		quota: leavePolicyData?.data.quota,
		isCountedAsPresent: leavePolicyData?.data.isCountedAsPresent?.toString(),
	};

	return (
		<FormProvider
			schema={updateLeavePolicySchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<LeavePolicyFormContent
				label="Edit Kebijakan Cuti"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default EditLeavePolicyPage;
