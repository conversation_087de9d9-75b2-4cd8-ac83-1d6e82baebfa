import { <PERSON>, <PERSON>ton, Grid, <PERSON>ack, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { SelectInput } from "@/shared/components/common/Form/SelectInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";

export const LeavePolicyFormContent: React.FC<{
	leavePolicyId?: string;
	isLoading: boolean;
	isSubmitting: boolean;
	label: string;
}> = ({ isLoading, isSubmitting, label }) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="name"
								label="<PERSON><PERSON>"
								placeholder="Masukkan nama kebija<PERSON> cuti"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="quota"
								label="Kuota"
								placeholder="Masukkan kuota cuti"
								type="number"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<SelectInput
								name="isCountedAsPresent"
								label="Dihitung Sebagai Hadir"
								placeholder="Pilih status"
								options={[
									{ label: "Ya", value: "true" },
									{ label: "Tidak", value: "false" },
								]}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12 }}>
							<TextInput
								name="description"
								label="Deskripsi"
								placeholder="Masukkan deskripsi kebijakan"
								isLoading={isLoading}
								minRows={4}
								multiline
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/leave-policies"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
