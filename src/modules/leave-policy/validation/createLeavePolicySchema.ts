import z from "zod";

export const createLeavePolicySchema = z.object({
	name: z.string().nonempty({ message: "Nama kebijakan wajib diisi" }),
	description: z.string().nonempty({ message: "Deskripsi wajib diisi" }),
	quota: z.coerce.number().min(1, { message: "Kuota harus minimal 1" }),
	isCountedAsPresent: z
		.string()
		.nonempty({ message: "Status kehadiran wajib dipilih" })
		.transform((val) => val === "true"),
});

export type CreateLeavePolicySchema = z.infer<typeof createLeavePolicySchema>;
