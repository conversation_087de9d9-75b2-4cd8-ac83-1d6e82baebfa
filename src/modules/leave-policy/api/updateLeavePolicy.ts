import { apiClient } from "@/shared/api/apiClient";
import type {
	UpdateLeavePolicyPayload,
	UpdateLeavePolicyResponse,
} from "@/shared/types/api";

export const updateLeavePolicy = async (
	leavePolicyId: string,
	payload: UpdateLeavePolicyPayload,
) => {
	const result = await apiClient.put<UpdateLeavePolicyResponse>(
		`/api/v1/admin/leave-policies/${leavePolicyId}`,
		payload,
	);
	return result;
};
