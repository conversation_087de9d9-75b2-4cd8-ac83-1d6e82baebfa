import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import { useAuth } from "@/shared/hooks";
import type {
	BaseErrorResponse,
	LoginPayload,
	LoginResponse,
} from "@/shared/types/api";

interface UseLoginOptions {
	onSuccessCallback?: (result: LoginResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useLogin = (options?: UseLoginOptions) => {
	const { login } = useAuth();

	return useMutation<
		LoginResponse,
		AxiosError<BaseErrorResponse>,
		LoginPayload
	>({
		mutationFn: login,
		onSuccess: async (result) => {
			options?.onSuccessCallback?.(result);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
