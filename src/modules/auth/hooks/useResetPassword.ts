import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	ResetPasswordPayload,
	ResetPasswordResponse,
} from "@/shared/types/api";
import { resetPassword } from "../api/resetPassword";

interface UseResetPasswordOptions {
	onSuccessCallback?: (data: ResetPasswordResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useResetPassword = (options?: UseResetPasswordOptions) => {
	return useMutation<
		ResetPasswordResponse,
		AxiosError<BaseErrorResponse>,
		ResetPasswordPayload
	>({
		mutationFn: resetPassword,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
