import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type { BaseErrorResponse, ForgotPasswordPayload, ForgotPasswordResponse } from "@/shared/types/api";
import { forgotPassword } from "../api/forgotPassword";

interface UseForgotPasswordOptions {
	onSuccessCallback?: (data: ForgotPasswordResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useForgotPassword = (options?: UseForgotPasswordOptions) => {
	return useMutation<ForgotPasswordResponse, AxiosError<BaseErrorResponse>, ForgotPasswordPayload>({
		mutationFn: forgotPassword,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
