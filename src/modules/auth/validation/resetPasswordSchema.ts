import * as z from "zod";

export const resetPasswordSchema = z
	.object({
		token: z.string(),
		newPassword: z.string().min(6, "Minimal 6 karakter"),
		newPasswordConfirmation: z.string().min(6, "Minimal 6 karakter"),
	})
	.refine((data) => data.newPassword === data.newPasswordConfirmation, {
		message: "Konfirmasi password baru tidak cocok",
		path: ["newPasswordConfirmation"],
	});
export type ResetPasswordSchema = z.infer<typeof resetPasswordSchema>;
