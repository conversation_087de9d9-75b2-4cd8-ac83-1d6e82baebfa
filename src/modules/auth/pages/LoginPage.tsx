import { zodResolver } from "@hookform/resolvers/zod";
import {
	Box,
	Button,
	Container,
	FormControl,
	FormHelperText,
	IconButton,
	InputAdornment,
	Link as MuiLink,
	OutlinedInput,
	Paper,
	Stack,
	Typography,
	useMediaQuery,
	useTheme,
} from "@mui/material";
import { nanoid } from "nanoid";
import type React from "react";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import {
	Link as ReactRouterLink,
	useNavigate,
	useSearchParams,
} from "react-router";
import { Autoplay, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import IconsaxEyeIcon from "@/assets/icons/iconsax-eye.svg?react";
import IconsaxEyeSlashIcon from "@/assets/icons/iconsax-eye-slash.svg?react";
import IconsaxLockIcon from "@/assets/icons/iconsax-lock.svg?react";
import IconsaxUserIcon from "@/assets/icons/iconsax-user.svg?react";
import IlustrationLogin1 from "@/assets/login-ilustration-1.webp";
import IlustrationLogin2 from "@/assets/login-ilustration-2.webp";
import IlustrationLogin3 from "@/assets/login-ilustration-3.webp";
import { Logo } from "@/shared/components/common/Logo";
import { useToast } from "@/shared/hooks";
import { useLogin } from "../hooks/useLogin";
import { type LoginSchema, loginSchema } from "../validation/loginSchema";

import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import { IconWrapper } from "@/shared/components/common/IconWrapper";

const items = [
	{ id: nanoid(), image: IlustrationLogin1 },
	{ id: nanoid(), image: IlustrationLogin2 },
	{ id: nanoid(), image: IlustrationLogin3 },
];

const LoginPage: React.FC = () => {
	const [showPassword, setShowPassword] = useState(false);
	const theme = useTheme();
	const isMobile = useMediaQuery(theme.breakpoints.down("md"));
	const { showToast } = useToast();

	const [searchParams] = useSearchParams();
	const navigate = useNavigate();
	const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";

	const {
		control,
		handleSubmit,
		formState: { errors, isValid },
	} = useForm<LoginSchema>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			credential: "",
			password: "",
		},
		mode: "onChange",
	});

	const mutation = useLogin({
		onSuccessCallback: () => {
			navigate(callbackUrl);
		},
		onErrorCallback: (error) => {
			showToast({
				message: error.response?.data.message || "Some error occur!",
				severity: "error",
			});
		},
	});

	const handleClickShowPassword = () => {
		setShowPassword((prev) => !prev);
	};
	const onSubmit = (data: LoginSchema) => {
		mutation.mutate(data);
	};

	return (
		<Container
			maxWidth={false}
			disableGutters
			sx={{ display: "flex", minHeight: "100vh", p: 0 }}
		>
			<Stack direction={{ xs: "column", md: "row" }} sx={{ width: "100%" }}>
				{/* Left Side: Form */}
				<Paper
					variant="outlined"
					sx={{
						width: "100%",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						position: "relative",
						borderRadius: 0,
						border: "none",
						height: { xs: "100vh", md: "100%" },
					}}
				>
					<Box sx={{ position: "absolute", top: "5%", left: "5%" }}>
						<Logo />
					</Box>
					<Stack
						direction="column"
						sx={{
							p: { xs: 3, sm: 4, md: 6 },
							width: "100%",
							maxWidth: 500,
						}}
					>
						<Typography
							variant="h2"
							component="h2"
							sx={{ fontSize: { xs: "2rem", md: "3rem" } }}
						>
							Login
						</Typography>
						<Typography
							variant="body1"
							sx={{ mt: 1, mb: 4, color: "text.disabled" }}
						>
							Selamat datang! Silakan login terlebih dahulu untuk mengelola
							semua aktivitas Absensi, Kehadiran, dan Performa karyawan Anda.
						</Typography>

						<form onSubmit={handleSubmit(onSubmit)}>
							<Stack spacing={2}>
								<Controller
									name="credential"
									control={control}
									render={({ field }) => (
										<FormControl fullWidth error={!!errors.credential}>
											<Typography variant="subtitle1">
												NIK/Nomor Telepon/Email
											</Typography>

											<OutlinedInput
												{...field}
												size="small"
												placeholder="Masukkan NIK/Nomor Telepon/Email…"
												startAdornment={
													<InputAdornment position="start">
														<IconWrapper icon={IconsaxUserIcon} />
													</InputAdornment>
												}
											/>

											{errors.credential && (
												<FormHelperText>
													{errors.credential.message}
												</FormHelperText>
											)}
										</FormControl>
									)}
								/>

								<Controller
									name="password"
									control={control}
									render={({ field }) => (
										<FormControl fullWidth error={!!errors.password}>
											<Typography variant="subtitle1">Password</Typography>

											<OutlinedInput
												{...field}
												size="small"
												type={showPassword ? "text" : "password"}
												placeholder="Masukkan password…"
												startAdornment={
													<InputAdornment position="start">
														<IconWrapper icon={IconsaxLockIcon} />
													</InputAdornment>
												}
												endAdornment={
													<InputAdornment position="end">
														<IconButton
															onClick={handleClickShowPassword}
															edge="end"
														>
															{showPassword ? (
																<IconWrapper icon={IconsaxEyeSlashIcon} />
															) : (
																<IconWrapper icon={IconsaxEyeIcon} />
															)}
														</IconButton>
													</InputAdornment>
												}
											/>

											{errors.password && (
												<FormHelperText>
													{errors.password.message}
												</FormHelperText>
											)}
										</FormControl>
									)}
								/>

								<Box sx={{ textAlign: "right" }}>
									<MuiLink
										variant="subtitle2"
										component={ReactRouterLink}
										to="/auth/forgot-password"
										underline="hover"
									>
										Forgot Password?
									</MuiLink>
								</Box>

								<Button
									type="submit"
									variant="contained"
									color="primary"
									disableElevation
									disabled={!isValid || mutation.isPending}
									loading={mutation.isPending}
									loadingPosition="start"
								>
									Login
								</Button>

								<Stack
									direction="row"
									spacing={0.5}
									justifyContent="center"
									alignItems="center"
								>
									<Typography variant="body2" component="p">
										Belum memiliki akun?
									</Typography>
									<MuiLink
										variant="subtitle2"
										component={ReactRouterLink}
										to="/auth/contact"
										underline="hover"
									>
										Hubungi Admin
									</MuiLink>
								</Stack>
							</Stack>
						</form>
					</Stack>
				</Paper>

				{/* Right Side: Illustration */}
				{!isMobile && (
					<Box
						sx={{
							width: "100%",
							backgroundColor: "primary.main",
							display: { xs: "none", md: "flex" },
							flexDirection: "column",
							justifyContent: "center",
							alignItems: "center",
							overflow: "hidden",
						}}
					>
						<Swiper
							loop
							modules={[Pagination, Autoplay]}
							pagination={{ clickable: true }}
							scrollbar={{ draggable: true }}
							autoplay={{ delay: 3000 }}
							style={{ width: "100%", height: "100%" }}
						>
							{items.map((item) => (
								<SwiperSlide key={item.id}>
									<Box
										sx={{
											width: "100%",
											height: "100%",
											display: "flex",
											flexDirection: "column",
											alignItems: "center",
											justifyContent: "center",
										}}
									>
										<Box
											component="img"
											src={item.image}
											alt="Ilustration"
											sx={{ maxWidth: 400, maxHeight: 400, objectFit: "cover" }}
										/>
										<Typography variant="h6">
											Manage Absensi Kariawan Anda
										</Typography>
										<Typography variant="body1" sx={{ color: "text.disabled" }}>
											Catat absensi dan kehadiran karyawan Anda dengan mudah dan
											cepat.
										</Typography>
									</Box>
								</SwiperSlide>
							))}
						</Swiper>
					</Box>
				)}
			</Stack>
		</Container>
	);
};

export default LoginPage;
