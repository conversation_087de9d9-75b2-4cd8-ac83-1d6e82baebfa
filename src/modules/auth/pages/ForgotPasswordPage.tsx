import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
	Box,
	Button,
	Container,
	FormControl,
	FormHelperText,
	InputAdornment,
	Link as MuiLink,
	OutlinedInput,
	Paper,
	Stack,
	Typography,
} from "@mui/material";
import type React from "react";
import { Controller, useForm } from "react-hook-form";
import { Link as ReactRouterLink } from "react-router";
import IconsaxUserIcon from "@/assets/icons/iconsax-user.svg?react";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { Logo } from "@/shared/components/common/Logo";
import { useToast } from "@/shared/hooks";
import { useForgotPassword } from "../hooks/useForgotPassword";
import {
	type ForgotPasswordSchema,
	forgotPasswordSchema,
} from "../validation/forgotPasswordSchema";

const ForgotPassword: React.FC = () => {
	const { showToast } = useToast();

	const {
		control,
		handleSubmit,
		formState: { errors, isValid },
	} = useForm<ForgotPasswordSchema>({
		resolver: zodResolver(forgotPasswordSchema),
		defaultValues: {
			email: "",
		},
		mode: "onChange",
	});

	const mutation = useForgotPassword({
		onSuccessCallback: () => {},
		onErrorCallback: (error) => {
			showToast({
				message: error.response?.data?.message || "Some error occur!",
				severity: "error",
			});
		},
	});

	const onSubmit = (data: ForgotPasswordSchema) => {
		mutation.mutate(data);
	};

	return (
		<Container
			maxWidth={false}
			disableGutters
			sx={{ display: "flex", position: "relative", minHeight: "100vh", p: 0 }}
		>
			<Box sx={{ position: "absolute", top: "3.5%", left: "2%" }}>
				<Logo />
			</Box>
			<Stack
				direction="column"
				alignItems="center"
				justifyContent="center"
				sx={{ flexGrow: 1 }}
			>
				<Paper variant="outlined" sx={{ borderWidth: { xs: 0, md: 1 } }}>
					<Stack
						direction="column"
						sx={{
							p: { xs: 3, sm: 4, md: 5 },
							width: "100%",
							maxWidth: 500,
						}}
					>
						<Typography
							variant="h2"
							component="h2"
							sx={{ fontSize: { xs: "2rem", md: "3rem" } }}
						>
							Lupa Password
						</Typography>
						<Typography
							variant="body1"
							sx={{ mt: 1, mb: 4, color: "text.disabled" }}
						>
							Silahkan masukkan email anda untuk mendapatkan pesan email untuk
							proses perubahan kata sandi.
						</Typography>

						<form onSubmit={handleSubmit(onSubmit)}>
							<Stack spacing={2}>
								<Controller
									name="email"
									control={control}
									render={({ field }) => (
										<FormControl fullWidth error={!!errors.email}>
											<Typography
												variant="body1"
												sx={{ fontWeight: 500, mb: 1 }}
											>
												Email
											</Typography>

											<OutlinedInput
												{...field}
												size="small"
												placeholder="Masukkan email anda"
												startAdornment={
													<InputAdornment position="start">
														<IconWrapper icon={IconsaxUserIcon} />
													</InputAdornment>
												}
											/>
											{errors.email && (
												<FormHelperText>{errors.email.message}</FormHelperText>
											)}
										</FormControl>
									)}
								/>

								<Button
									type="submit"
									variant="contained"
									color="primary"
									disableElevation
									disabled={!isValid || mutation.isPending}
								>
									Kirim
								</Button>

								<MuiLink
									variant="subtitle2"
									component={ReactRouterLink}
									to="/auth/login"
								>
									Kembali
								</MuiLink>
							</Stack>
						</form>
					</Stack>
				</Paper>
			</Stack>
		</Container>
	);
};

export default ForgotPassword;
