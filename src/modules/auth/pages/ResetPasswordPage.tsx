import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
	Box,
	Button,
	Container,
	FormControl,
	FormHelperText,
	IconButton,
	InputAdornment,
	Link as MuiLink,
	OutlinedInput,
	Paper,
	Stack,
	Typography,
} from "@mui/material";
import type React from "react";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Link as ReactRouterLink, useSearchParams } from "react-router";
import IconsaxEyeIcon from "@/assets/icons/iconsax-eye.svg?react";
import IconsaxEyeSlashIcon from "@/assets/icons/iconsax-eye-slash.svg?react";
import IconsaxLockIcon from "@/assets/icons/iconsax-lock.svg?react";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { Logo } from "@/shared/components/common/Logo";
import { useToast } from "@/shared/hooks";
import { useResetPassword } from "../hooks/useResetPassword";
import {
	type ResetPasswordSchema,
	resetPasswordSchema,
} from "../validation/resetPasswordSchema";

const ResetPassword: React.FC = () => {
	const { showToast } = useToast();
	const [searchParams] = useSearchParams();
	const token = searchParams.get("token") ?? "invalid_token_123";

	const [showNewPassword, setShowNewPassword] = useState(false);
	const [showNewPasswordConfirmation, setShowNewPasswordConfirmation] =
		useState(false);

	const handleClickShowNewPassword = () => {
		setShowNewPassword((prev) => !prev);
	};

	const handleClickShowNewPasswordConfirmation = () => {
		setShowNewPasswordConfirmation((prev) => !prev);
	};

	const {
		control,
		handleSubmit,
		formState: { errors, isValid },
	} = useForm<ResetPasswordSchema>({
		resolver: zodResolver(resetPasswordSchema),
		defaultValues: {
			token: "",
			newPassword: "",
			newPasswordConfirmation: "",
		},
		mode: "onChange",
	});

	const mutation = useResetPassword({
		onSuccessCallback: () => {},
		onErrorCallback: (error) => {
			showToast({
				message: error.response?.data?.message || "Some error occur!",
				severity: "error",
			});
		},
	});

	const onSubmit = (data: ResetPasswordSchema) => {
		mutation.mutate({
			...data,
			token,
		});
	};

	return (
		<Container
			maxWidth={false}
			disableGutters
			sx={{ display: "flex", position: "relative", minHeight: "100vh", p: 0 }}
		>
			<Box sx={{ position: "absolute", top: "3.5%", left: "2%" }}>
				<Logo />
			</Box>
			<Stack
				direction="column"
				alignItems="center"
				justifyContent="center"
				sx={{ flexGrow: 1 }}
			>
				<Paper variant="outlined" sx={{ borderWidth: { xs: 0, md: 1 } }}>
					<Stack
						direction="column"
						sx={{
							p: { xs: 3, sm: 4, md: 5 },
							width: "100%",
							maxWidth: 500,
						}}
					>
						<Typography
							variant="h2"
							component="h2"
							sx={{ fontSize: { xs: "2rem", md: "3rem" } }}
						>
							Reset Password
						</Typography>
						<Typography
							variant="body1"
							sx={{ mt: 1, mb: 4, color: "text.disabled" }}
						>
							Masukkan kata sandi baru anda untuk mengatur ulang kata sandi yang
							anda miliki.
						</Typography>

						<form onSubmit={handleSubmit(onSubmit)}>
							<Stack spacing={2}>
								<Controller
									name="newPassword"
									control={control}
									render={({ field }) => (
										<FormControl fullWidth error={!!errors.newPassword}>
											<Typography
												variant="body1"
												sx={{ fontWeight: 500, mb: 1 }}
											>
												New Password
											</Typography>

											<OutlinedInput
												{...field}
												size="small"
												type={showNewPassword ? "text" : "password"}
												placeholder="Masukkan password baru..."
												startAdornment={
													<InputAdornment position="start">
														<IconWrapper icon={IconsaxLockIcon} />
													</InputAdornment>
												}
												endAdornment={
													<InputAdornment position="end">
														<IconButton
															onClick={handleClickShowNewPassword}
															edge="end"
														>
															{showNewPassword ? (
																<IconWrapper icon={IconsaxEyeSlashIcon} />
															) : (
																<IconWrapper icon={IconsaxEyeIcon} />
															)}
														</IconButton>
													</InputAdornment>
												}
											/>
											{errors.newPassword && (
												<FormHelperText>
													{errors.newPassword.message}
												</FormHelperText>
											)}
										</FormControl>
									)}
								/>

								<Controller
									name="newPasswordConfirmation"
									control={control}
									render={({ field }) => (
										<FormControl
											fullWidth
											error={!!errors.newPasswordConfirmation}
										>
											<Typography
												variant="body1"
												sx={{ fontWeight: 500, mb: 1 }}
											>
												New Password Confirmation
											</Typography>

											<OutlinedInput
												{...field}
												size="small"
												type={showNewPasswordConfirmation ? "text" : "password"}
												placeholder="Masukkan ulang password..."
												startAdornment={
													<InputAdornment position="start">
														<IconWrapper icon={IconsaxLockIcon} />
													</InputAdornment>
												}
												endAdornment={
													<InputAdornment position="end">
														<IconButton
															onClick={handleClickShowNewPasswordConfirmation}
															edge="end"
														>
															{showNewPasswordConfirmation ? (
																<IconWrapper icon={IconsaxEyeSlashIcon} />
															) : (
																<IconWrapper icon={IconsaxEyeIcon} />
															)}
														</IconButton>
													</InputAdornment>
												}
											/>
											{errors.newPasswordConfirmation && (
												<FormHelperText>
													{errors.newPasswordConfirmation.message}
												</FormHelperText>
											)}
										</FormControl>
									)}
								/>

								<Button
									type="submit"
									variant="contained"
									color="primary"
									disableElevation
									disabled={!isValid || mutation.isPending}
								>
									Konfirmasi
								</Button>

								<MuiLink
									variant="subtitle2"
									component={ReactRouterLink}
									to="/auth/login"
								>
									Kembali
								</MuiLink>
							</Stack>
						</form>
					</Stack>
				</Paper>
			</Stack>
		</Container>
	);
};

export default ResetPassword;
