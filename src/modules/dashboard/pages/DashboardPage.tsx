import { Box, Link, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";

const DashboardPage: React.FC = () => {
	return (
		<Box sx={{ pb: 100 }}>
			<Typography variant="h6" component="h2">
				Dashboard
			</Typography>
			Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptate ea odit
			veritatis porro fugit doloremque soluta provident laudantium accusantium
			perferendis. Ipsum dolore harum soluta accusamus, placeat ducimus corporis
			quae numquam tempore fugiat fuga provident asperiores eveniet odio error
			hic incidunt corrupti quasi? Similique repellat magni in, cupiditate
			officia eius necessitatibus blanditiis libero odio debitis quis animi
			mollitia. Cupiditate reiciendis blanditiis commodi dolore quisquam nihil
			perferendis alias, cumque numquam expedita, ipsum enim explicabo
			voluptatem harum inventore eum asperiores esse quo! Commodi vel recusandae
			suscipit harum earum dignissimos perspiciatis iure, asperiores sunt iste
			vero, labore quasi quidem perferendis quaerat reiciendis facere
			reprehenderit?
			<Link component={ReactRouterLink} to="/auth/login">
				Go to Login
			</Link>
			{/* <OrgChartTree /> */}
		</Box>
	);
};

export default DashboardPage;
