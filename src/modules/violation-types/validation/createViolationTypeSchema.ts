import z from "zod";

export const createViolationTypeSchema = z.object({
	name: z.string().nonempty({ message: "Nama wajib diisi" }),
	description: z.string().optional(),
	penaltyPoints: z.coerce
		.number()
		.min(0, { message: "Poin penalti tidak boleh negatif" }),
	punishment: z.string().nonempty({ message: "Hukuman wajib diisi" }),
});

export type CreateViolationTypeSchema = z.infer<
	typeof createViolationTypeSchema
>;
