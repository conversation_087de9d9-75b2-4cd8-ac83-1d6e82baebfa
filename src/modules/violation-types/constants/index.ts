import type { SelectOption } from "@/shared/types/common";

export const ViolationTypeStatus = {
	ACTIVE: true,
	INACTIVE: false,
} as const;

export const ViolationTypeStatusLabel: Record<string, string> = {
	true: "Aktif",
	false: "Tidak Aktif",
};

export const violationTypeStatusSelectOption: SelectOption[] = [
	{
		label: "Aktif",
		value: "true",
	},
	{
		label: "Tidak Aktif",
		value: "false",
	},
];
