import { apiClient } from "@/shared/api/apiClient";
import type {
	UpdateViolationTypePayload,
	UpdateViolationTypeResponse,
} from "@/shared/types/api";

export const updateViolationType = async (
	violationTypeId: string,
	payload: UpdateViolationTypePayload,
) => {
	const result = await apiClient.put<UpdateViolationTypeResponse>(
		`/api/v1/admin/violation-types/${violationTypeId}`,
		payload,
	);
	return result;
};
