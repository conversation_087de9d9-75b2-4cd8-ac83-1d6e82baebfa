import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateViolationTypePayload,
	UpdateViolationTypeResponse,
} from "@/shared/types/api";
import { updateViolationType } from "../api/updateViolationType";

interface UseUpdateViolationTypeOptions {
	onSuccessCallback?: (data: UpdateViolationTypeResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateViolationType = (options?: UseUpdateViolationTypeOptions) => {
	return useMutation<
		UpdateViolationTypeResponse,
		AxiosError<BaseErrorResponse>,
		{ violationTypeId: string; payload: UpdateViolationTypePayload }
	>({
		mutationFn: ({ violationTypeId, payload }) => updateViolationType(violationTypeId, payload),
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
