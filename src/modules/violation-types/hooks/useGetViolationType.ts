import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetViolationTypeResponse,
} from "@/shared/types/api";
import { getViolationType } from "../api/getViolationType";

export const useGetViolationType = (
	violationTypeId: string,
	options?: Omit<
		UseQueryOptions<GetViolationTypeResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getViolationType", violationTypeId],
		queryFn: async () => {
			return getViolationType(violationTypeId);
		},
		...options,
	});
};
