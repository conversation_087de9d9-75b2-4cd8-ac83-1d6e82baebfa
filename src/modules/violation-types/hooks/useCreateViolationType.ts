import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateViolationTypePayload,
	CreateViolationTypeResponse,
} from "@/shared/types/api";
import { createViolationType } from "../api/createViolationType";

interface UseCreateViolationTypeOptions {
	onSuccessCallback?: (data: CreateViolationTypeResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateViolationType = (options?: UseCreateViolationTypeOptions) => {
	return useMutation<
		CreateViolationTypeResponse,
		AxiosError<BaseErrorResponse>,
		CreateViolationTypePayload
	>({
		mutationFn: createViolationType,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
