import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	BulkActionPayload,
	BulkActionResponse,
} from "@/shared/types/api";
import { bulkActionViolationType } from "../api/bulkActionViolationType";

interface UseBulkActionViolationTypeOptions {
	onSuccessCallback?: (data: BulkActionResponse | Blob) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useBulkActionViolationType = (
	options?: UseBulkActionViolationTypeOptions,
) => {
	return useMutation<
		BulkActionResponse | Blob,
		AxiosError<BaseErrorResponse>,
		BulkActionPayload
	>({
		mutationFn: bulkActionViolationType,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
