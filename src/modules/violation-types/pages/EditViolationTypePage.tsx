import { useParams } from "react-router";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import { ViolationTypeFormContent } from "../components/ViolationTypeFormContent";
import { useGetViolationType } from "../hooks/useGetViolationType";
import { useUpdateViolationType } from "../hooks/useUpdateViolationType";
import {
	type UpdateViolationTypeSchema,
	updateViolationTypeSchema,
} from "../validation/updateViolationTypeSchema";

const EditViolationTypePage: React.FC = () => {
	const violationTypeId = useParams().violationTypeId as string;

	const { data: violationTypeData, isLoading: isViolationTypeLoading } =
		useGetViolationType(violationTypeId);

	const isLoading = isViolationTypeLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useUpdateViolationType({
		onSuccessCallback: (data) =>
			handleSuccess(data.message, "/violation-types"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: UpdateViolationTypeSchema) => {
		mutation.mutate({ violationTypeId, payload: data });
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !violationTypeData?.data) {
		return (
			<NotFoundPage
				resourceType="Jenis Pelanggaran"
				redirectTo="/violation-types"
			/>
		);
	}

	const defaultValues: UpdateViolationTypeSchema = {
		name: violationTypeData?.data.name,
		description: violationTypeData?.data.description || undefined,
		penaltyPoints: violationTypeData?.data.penaltyPoints,
		punishment: violationTypeData?.data.punishment,
		// isActive: violationTypeData?.data.isActive,
	};

	return (
		<FormProvider
			schema={updateViolationTypeSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<ViolationTypeFormContent
				label="Edit Jenis Pelanggaran"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default EditViolationTypePage;
