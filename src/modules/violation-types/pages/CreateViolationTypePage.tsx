import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import { ViolationTypeFormContent } from "../components/ViolationTypeFormContent";
import { useCreateViolationType } from "../hooks/useCreateViolationType";
import {
	type CreateViolationTypeSchema,
	createViolationTypeSchema,
} from "../validation/createViolationTypeSchema";

const CreateViolationTypePage: React.FC = () => {
	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateViolationType({
		onSuccessCallback: (data) => handleSuccess(data.message, "/violation-types"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateViolationTypeSchema) => {
		mutation.mutate(data);
	};

	const defaultValues: CreateViolationTypeSchema = {
		name: "",
		description: "",
		penaltyPoints: 0,
		punishment: "",
		// isActive: true,
	};

	return (
		<FormProvider
			schema={createViolationTypeSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<ViolationTypeFormContent
				label="Tambah Jenis Pelanggaran"
				isLoading={false}
				isSubmitting={mutation.isPending}
			/>
		</FormProvider>
	);
};

export default CreateViolationTypePage;
