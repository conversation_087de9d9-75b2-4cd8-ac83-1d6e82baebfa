import { <PERSON>, <PERSON>ton, Grid, <PERSON><PERSON>, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";

export const ViolationTypeFormContent: React.FC<{
	violationTypeId?: string;
	isLoading: boolean;
	isSubmitting: boolean;
	label: string;
}> = ({ isLoading, isSubmitting, label }) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="name"
								label="Nama <PERSON>"
								placeholder="Masukkan nama jenis pelanggaran"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="penaltyPoints"
								label="Poin Penalti"
								placeholder="Masukkan poin penalti"
								isLoading={isLoading}
								type="number"
								inputProps={{ min: 0 }}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="punishment"
								label="Hukuman"
								placeholder="Masukkan jenis hukuman"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12 }}>
							<TextInput
								name="description"
								label="Deskripsi"
								placeholder="Masukkan deskripsi jenis pelanggaran"
								isLoading={isLoading}
								minRows={4}
								multiline
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/violation-types"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
