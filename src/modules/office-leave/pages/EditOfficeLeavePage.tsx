import { useParams } from "react-router";
import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import NotFoundPage from "@/shared/pages/404";
import type { SelectOption } from "@/shared/types/common";
import { OfficeLeaveFormContent } from "../components/OfficeLeaveFormContent";
import { useCreateOfficeLeave } from "../hooks/useCreateOfficeLeave";
import { useGetOfficeLeave } from "../hooks/useGetOfficeLeave";
import {
	type CreateOfficeLeaveSchema,
	createOfficeLeaveSchema,
} from "../validation/createOfficeLeaveSchema";
import type { UpdateOfficeLeaveSchema } from "../validation/updateOfficeLeaveSchema";

const EditOfficeLeavePage: React.FC = () => {
	const officeLeaveId = useParams().officeLeaveId as string;

	const { data: officeLeaveData, isLoading: isOfficeLeaveLoading } =
		useGetOfficeLeave(officeLeaveId);
	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();

	const isLoading = isUserOptionsLoading || isOfficeLeaveLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateOfficeLeave({
		onSuccessCallback: (data) => handleSuccess(data.message, "/office-leaves"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateOfficeLeaveSchema) => {
		mutation.mutate(data);
	};

	if (isLoading) {
		return <FullPageLoader />;
	}

	if (!isLoading && !officeLeaveData?.data) {
		return (
			<NotFoundPage resourceType="Izin Keluar" redirectTo="/office-leaves" />
		);
	}

	const defaultValues: UpdateOfficeLeaveSchema = {
		userId: officeLeaveData?.data.userId,
		title: officeLeaveData?.data.title,
		description: officeLeaveData?.data.description,
		startTime: officeLeaveData?.data.startTime,
		endTime: officeLeaveData?.data.endTime,
		date: officeLeaveData?.data.date,
	};

	return (
		<FormProvider
			schema={createOfficeLeaveSchema}
			defaultValues={defaultValues}
			onSubmit={onSubmit}
		>
			<OfficeLeaveFormContent
				label="Edit Izin Keluar"
				isLoading={false}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default EditOfficeLeavePage;
