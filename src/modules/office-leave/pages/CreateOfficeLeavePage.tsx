import { useGetUserOptions } from "@/modules/users/hooks/useGetUserOptions";
import { FormProvider } from "@/shared/components/common/Form/FormProvider";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { SelectOption } from "@/shared/types/common";
import { OfficeLeaveFormContent } from "../components/OfficeLeaveFormContent";
import { useCreateOfficeLeave } from "../hooks/useCreateOfficeLeave";
import {
	type CreateOfficeLeaveSchema,
	createOfficeLeaveSchema,
} from "../validation/createOfficeLeaveSchema";

const CreateOfficeLeavePage: React.FC = () => {
	const { data: userOptionsData, isLoading: isUserOptionsLoading } =
		useGetUserOptions();

	const isLoading = isUserOptionsLoading;

	const { handleSuccess, handleError } = useMutationCallbacks();
	const mutation = useCreateOfficeLeave({
		onSuccessCallback: (data) => handleSuccess(data.message, "/office-leaves"),
		onErrorCallback: handleError,
	});

	const onSubmit = (data: CreateOfficeLeaveSchema) => {
		mutation.mutate(data);
	};

	return (
		<FormProvider
			schema={createOfficeLeaveSchema}
			defaultValues={{
				userId: "",
				title: "",
				description: "",
			}}
			onSubmit={onSubmit}
		>
			<OfficeLeaveFormContent
				label="Buat Izin Keluar Baru"
				isLoading={isLoading}
				isSubmitting={mutation.isPending}
				userOptions={userOptionsData?.data as SelectOption[]}
			/>
		</FormProvider>
	);
};

export default CreateOfficeLeavePage;
