import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	CreateOfficeLeavePayload,
	CreateOfficeLeaveResponse,
} from "@/shared/types/api";
import { createOfficeLeave } from "../api/createOfficeLeave";

interface UseCreateOfficeLeaveOptions {
	onSuccessCallback?: (data: CreateOfficeLeaveResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useCreateOfficeLeave = (options?: UseCreateOfficeLeaveOptions) => {
	return useMutation<
		CreateOfficeLeaveResponse,
		AxiosError<BaseErrorResponse>,
		CreateOfficeLeavePayload
	>({
		mutationFn: createOfficeLeave,
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
