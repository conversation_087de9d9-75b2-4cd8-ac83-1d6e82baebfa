import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	GetOfficeLeaveResponse,
} from "@/shared/types/api";
import { getOfficeLeave } from "../api/getOfficeLeave";

export const useGetOfficeLeave = (
	officeLeaveId: string,
	options?: Omit<
		UseQueryOptions<GetOfficeLeaveResponse, AxiosError<BaseErrorResponse>>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["getOfficeLeave", officeLeaveId],
		queryFn: async () => {
			return getOfficeLeave(officeLeaveId);
		},
		...options,
	});
};
