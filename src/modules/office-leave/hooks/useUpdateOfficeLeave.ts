import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	UpdateOfficeLeavePayload,
	UpdateOfficeLeaveResponse,
} from "@/shared/types/api";
import { updateOfficeLeave } from "../api/updateOfficeLeave";

interface UseUpdateOfficeLeaveOptions {
	onSuccessCallback?: (data: UpdateOfficeLeaveResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useUpdateOfficeLeave = (options?: UseUpdateOfficeLeaveOptions) => {
	return useMutation<
		UpdateOfficeLeaveResponse,
		AxiosError<BaseErrorResponse>,
		{ officeLeaveId: string; payload: UpdateOfficeLeavePayload }
	>({
		mutationFn: ({ officeLeaveId, payload }) => {
			return updateOfficeLeave(officeLeaveId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
