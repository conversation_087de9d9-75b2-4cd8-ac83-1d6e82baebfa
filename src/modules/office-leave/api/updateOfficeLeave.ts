import { apiClient } from "@/shared/api/apiClient";
import type {
	UpdateOfficeLeavePayload,
	UpdateOfficeLeaveResponse,
} from "@/shared/types/api";

export const updateOfficeLeave = async (
	officeLeaveId: string,
	payload: UpdateOfficeLeavePayload,
) => {
	const result = await apiClient.put<UpdateOfficeLeaveResponse>(
		`/api/v1/admin/office-leaves/${officeLeaveId}`,
		payload,
	);
	return result;
};
