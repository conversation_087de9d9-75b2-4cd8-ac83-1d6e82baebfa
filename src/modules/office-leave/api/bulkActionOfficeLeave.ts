import { apiClient } from "@/shared/api/apiClient";
import type { BulkActionPayload, BulkActionResponse } from "@/shared/types/api";

export const bulkActionOfficeLeave = async (
	payload: BulkActionPayload,
): Promise<BulkActionResponse | Blob> => {
	const isExport = payload.action === "export";

	const result = await apiClient.post<string | BulkActionResponse>(
		"/api/v1/admin/office-leaves/bulk-action",
		payload,
		{
			responseType: isExport ? "blob" : "json",
		},
	);

	// Jika export, balikin Blob agar bisa di-download
	if (isExport && typeof result === "string") {
		return new Blob([result], { type: "text/csv" });
	}

	return result as BulkActionResponse;
};
