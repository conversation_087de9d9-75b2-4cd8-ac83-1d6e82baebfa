import type { SelectOption } from "@/shared/types/common";

export const OfficeLeaveStatus = {
	NEED_REVIEW: "NEED_REVIEW",
	REVIEWED: "REVIEWED",
} as const;

export const OfficeLeaveStatusLabel: Record<
	keyof typeof OfficeLeaveStatus,
	string
> = {
	NEED_REVIEW: "Butuh Review",
	REVIEWED: "Sudah di Review",
};

export const officeLeaveStatusSelectOption: SelectOption[] = [
	{
		label: "Butuh Review",
		value: "NEED_REVIEW",
	},
	{
		label: "Sudah di Review",
		value: "REVIEWED",
	},
];
