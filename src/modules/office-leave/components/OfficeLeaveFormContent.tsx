import { <PERSON>, Button, Grid, <PERSON>ack, Typography } from "@mui/material";
import { Link as ReactRouterLink } from "react-router";
import { Footer } from "@/shared/components/common/Footer";
import { AutocompleteSelectInput } from "@/shared/components/common/Form/AutoCompleteSelectInput";
import { DateInput } from "@/shared/components/common/Form/DateInput";
import { TextInput } from "@/shared/components/common/Form/TextInput";
import { TimeInput } from "@/shared/components/common/Form/TimeInput";
import { ButtonSkeleton } from "@/shared/components/common/Skeleton/ButtonSkeleton";
import type { SelectOption } from "@/shared/types/common";

export const OfficeLeaveFormContent: React.FC<{
	officeLeaveId?: string;
	isLoading: boolean;
	isSubmitting: boolean;
	label: string;
	userOptions: SelectOption[];
}> = ({ isLoading, isSubmitting, label, userOptions }) => {
	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				{label}
			</Typography>

			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflowX: "auto",
				}}
			>
				<Box>
					<Grid container spacing={2}>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<AutocompleteSelectInput
								name="userId"
								label="User"
								placeholder="Cari dan pilih opsi"
								options={userOptions}
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<DateInput name="date" label="Tanggal" isLoading={isLoading} />
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TimeInput
								name="startTime"
								label="Jam Keluar"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TimeInput
								name="endTime"
								label="Jam Kembali"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="title"
								label="Judul"
								placeholder="Masukkan judul izin keluar"
								isLoading={isLoading}
							/>
						</Grid>
						<Grid size={{ xs: 12, md: 6, lg: 4 }}>
							<TextInput
								name="description"
								label="Deskripsi"
								placeholder="Masukkan deskripsi"
								isLoading={isLoading}
								minRows={4}
								multiline
							/>
						</Grid>
					</Grid>
				</Box>

				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="end"
					sx={{ mt: 4 }}
				>
					{isLoading ? (
						<>
							<ButtonSkeleton size="medium" />
							<ButtonSkeleton size="medium" />
						</>
					) : (
						<>
							<Button
								component={ReactRouterLink}
								to="/office-leaves"
								variant="contained"
								color="error"
								sx={{ px: 3 }}
								disabled={isSubmitting}
							>
								Batal
							</Button>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								sx={{ px: 3 }}
								loading={isSubmitting}
								loadingPosition="start"
							>
								Simpan
							</Button>
						</>
					)}
				</Stack>
			</Box>

			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};
