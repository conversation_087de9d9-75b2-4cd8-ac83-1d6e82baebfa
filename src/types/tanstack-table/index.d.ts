/** biome-ignore-all lint/correctness/noUnusedVariables: <""> */
import type { FilterVariant } from "@/shared/constants/dataTable";
import type { SelectOption } from "@/shared/types/common";
import "@tanstack/react-table";

interface CustomColumnMeta {
	columnLabel: string;
	filterVariant?: FilterVariant;
	selectOptions?: SelectOption[];
	isMultipleSelect?: boolean;
}

// Module augmentation untuk extend TanStack Table types
declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> extends CustomColumnMeta {}
}
