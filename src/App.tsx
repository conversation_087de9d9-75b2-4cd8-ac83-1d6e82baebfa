import { Box } from "@mui/material";
import { createBrowserRouter, Navigate, RouterProvider } from "react-router";
import AttendanceConfigurationPage from "./modules/attendance/pages/AttendanceConfigurationPage";
import AttendanceLogListPage from "./modules/attendance/pages/AttendanceLogListPage";
import CreateAttendanceLogPage from "./modules/attendance/pages/CreateAttendanceLogPage";
import EditAttendanceLogPage from "./modules/attendance/pages/EditAttendanceLogPage";
import ForgotPassword from "./modules/auth/pages/ForgotPasswordPage";
import LoginPage from "./modules/auth/pages/LoginPage";
import ResetPassword from "./modules/auth/pages/ResetPasswordPage";
import DashboardPage from "./modules/dashboard/pages/DashboardPage";
import CreateLeaveRequestPage from "./modules/leave/pages/CreateLeaveRequestPage";
import EditLeaveRequestPage from "./modules/leave/pages/EditLeaveRequestPage";
import LeaveRequestListPage from "./modules/leave/pages/LeaveRequestListPage";
import CreateLeavePolicyPage from "./modules/leave-policy/pages/CreateLeavePolicyPage";
import EditLeavePolicyPage from "./modules/leave-policy/pages/EditLeavePolicyPage";
import LeavePolicyListPage from "./modules/leave-policy/pages/LeavePolicyListPage";
import CreateOfficeLeavePage from "./modules/office-leave/pages/CreateOfficeLeavePage";
import EditOfficeLeavePage from "./modules/office-leave/pages/EditOfficeLeavePage";
import OfficeLeaveListPage from "./modules/office-leave/pages/OfficeLeaveListPage";
import CreateRolePage from "./modules/roles/pages/CreateRolePage";
import EditRolePage from "./modules/roles/pages/EditRolePage";
import RoleListPage from "./modules/roles/pages/RoleListPage";
import RoleTreePage from "./modules/roles/pages/RoleTreePage";
import CreateTaskPage from "./modules/task/pages/CreateTaskPage";
import EditTaskPage from "./modules/task/pages/EditTaskPage";
import TaskListPage from "./modules/task/pages/TaskListPage";
import AdministratorUserListPage from "./modules/users/pages/AdministratorUserListPage";
import CreateAdministratorUserPage from "./modules/users/pages/CreateAdministratorUserPage";
import CreateNewUserPage from "./modules/users/pages/CreateUserPage";
import EditAdministratorUserPage from "./modules/users/pages/EditAdministratorUserPage";
import EditUserPage from "./modules/users/pages/EditUserPage";
import UserListPage from "./modules/users/pages/UserListPage";
import UserTreePage from "./modules/users/pages/UserTreePage";
import {
	CreateViolationTypePage,
	EditViolationTypePage,
	ViolationTypeListPage,
} from "./modules/violation-types/pages";
import { AppLayout } from "./shared/components/layouts/AppLayout";
import { DashboardLayout } from "./shared/components/layouts/DashboardLayout";
import { AuthRouteGuard } from "./shared/components/routes/AuthRouteGuard";
import { ProtectedRouteGuard } from "./shared/components/routes/ProtectedRouteGuard";
import NotFoundPage from "./shared/pages/404";

const router = createBrowserRouter([
	{
		element: <AppLayout />,
		children: [
			{
				path: "/",
				element: <Navigate to="/dashboard" />,
			},
			{
				element: <AuthRouteGuard />,
				children: [
					{
						path: "/auth/login",
						element: <LoginPage />,
					},
					{
						path: "/auth/forgot-password",
						element: <ForgotPassword />,
					},
					{
						path: "/auth/reset-password",
						element: <ResetPassword />,
					},
				],
			},
			{
				element: <ProtectedRouteGuard />,
				children: [
					{
						element: <DashboardLayout />,
						children: [
							{
								index: true,
								path: "/dashboard",
								element: <DashboardPage />,
							},
							{
								path: "/users",
								children: [
									{
										index: true,
										element: <UserListPage />,
									},
									{
										path: "/users/administrators",
										element: <AdministratorUserListPage />,
									},
									{
										path: "/users/hierarchy",
										element: <UserTreePage />,
									},
									{
										path: "/users/new",
										element: <CreateNewUserPage />,
									},
									{
										path: "/users/administrators/new",
										element: <CreateAdministratorUserPage />,
									},
									{
										path: "/users/:userId/edit",
										element: <EditUserPage />,
									},
									{
										path: "/users/administrators/:administratorUserId/edit",
										element: <EditAdministratorUserPage />,
									},
								],
							},

							{
								path: "/organizations",
								children: [
									{
										index: true,
										element: <RoleListPage />,
									},
									{
										path: "/organizations/new",
										element: <CreateRolePage />,
									},
									{
										path: "/organizations/hierarchy",
										element: <RoleTreePage />,
									},
									{
										path: "/organizations/:roleId/edit",
										element: <EditRolePage />,
									},
								],
							},
							{
								path: "/attendance",
								children: [
									{
										index: true,
										element: <AttendanceLogListPage />,
									},
									{
										path: "/attendance/new",
										element: <CreateAttendanceLogPage />,
									},
									{
										path: "/attendance/:attendanceLogId/edit",
										element: <EditAttendanceLogPage />,
									},
									{
										path: "/attendance/configurations",
										element: <AttendanceConfigurationPage />,
									},
								],
							},
							{
								path: "/office-leaves",
								children: [
									{
										index: true,
										element: <OfficeLeaveListPage />,
									},
									{
										path: "/office-leaves/new",
										element: <CreateOfficeLeavePage />,
									},
									{
										path: "/office-leaves/:officeLeaveId/edit",
										element: <EditOfficeLeavePage />,
									},
								],
							},
							{
								path: "/leaves",
								children: [
									{
										index: true,
										element: <LeaveRequestListPage />,
									},
									{
										path: "/leaves/new",
										element: <CreateLeaveRequestPage />,
									},
									{
										path: "/leaves/:leaveRequestId/edit",
										element: <EditLeaveRequestPage />,
									},
								],
							},
							{
								path: "/leave-policies",
								children: [
									{
										index: true,
										element: <LeavePolicyListPage />,
									},
									{
										path: "/leave-policies/new",
										element: <CreateLeavePolicyPage />,
									},
									{
										path: "/leave-policies/:leavePolicyId/edit",
										element: <EditLeavePolicyPage />,
									},
								],
							},
							{
								path: "/tasks",
								children: [
									{
										index: true,
										element: <TaskListPage />,
									},
									{
										path: "/tasks/new",
										element: <CreateTaskPage />,
									},
									{
										path: "/tasks/:taskId/edit",
										element: <EditTaskPage />,
									},
								],
							},
							{
								path: "/violation-types",
								children: [
									{
										index: true,
										element: <ViolationTypeListPage />,
									},
									{
										path: "/violation-types/new",
										element: <CreateViolationTypePage />,
									},
									{
										path: "/violation-types/:violationTypeId/edit",
										element: <EditViolationTypePage />,
									},
								],
							},
						],
					},
				],
			},
			{
				path: "*",
				element: <NotFoundPage />,
			},
		],
	},
]);

const App: React.FC = () => {
	return (
		<Box sx={{ minHeight: "100vh", backgroundColor: "background.default" }}>
			<RouterProvider router={router} />
		</Box>
	);
};

export default App;
