import { useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router";
import { useAuth } from "@/shared/hooks";
import FullPageLoader from "../common/FullPageLoader";

export const AuthRouteGuard: React.FC = () => {
	const { isLoading, isAuthenticated } = useAuth();
	const navigate = useNavigate();
	const location = useLocation();

	useEffect(() => {
		if (!isLoading && isAuthenticated) {
			const callbackUrl = new URLSearchParams(location.search).get("callbackUrl");
			navigate(callbackUrl || "/dashboard", { replace: true });
		}
	}, [isAuthenticated, isLoading, location.search, navigate]);

	if (isLoading) return <FullPageLoader />;
	if (isAuthenticated) return null;

	return <Outlet />;
};
