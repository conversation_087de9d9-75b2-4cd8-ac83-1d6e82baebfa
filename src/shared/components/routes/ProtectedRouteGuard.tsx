import { useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { useAuth } from "@/shared/hooks";

export const ProtectedRouteGuard: React.FC = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const { isAuthenticated, isLoading } = useAuth();

	useEffect(() => {
		if (!isLoading && !isAuthenticated) {
			const callbackUrl = encodeURIComponent(location.pathname + location.search);
			navigate(`/auth/login?callbackUrl=${callbackUrl}`, { replace: true });
		}
	}, [isAuthenticated, isLoading, location.pathname, location.search, navigate]);

	if (isLoading) return <FullPageLoader />;
	if (!isAuthenticated) return null;

	return <Outlet />;
};
