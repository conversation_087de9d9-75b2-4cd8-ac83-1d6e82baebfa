import { Checkbox, FormControl, FormControlLabel, FormHelperText, Typography } from "@mui/material";
import type React from "react";
import { Controller, useFormContext } from "react-hook-form";

interface CheckboxInputProps {
	name: string;
	label: string;
	placeholder?: string;
	helperText?: React.ReactNode;
	isLoading?: boolean;
}

export const CheckboxInput: React.FC<CheckboxInputProps> = ({ name, label, helperText }) => {
	const {
		control,
		formState: { errors },
	} = useFormContext();

	return (
		<Controller
			name={name}
			control={control}
			render={({ field }) => (
				<FormControl error={!!errors[name]}>
					<FormControlLabel
						control={<Checkbox {...field} checked={!!field.value} />}
						label={
							<Typography variant="subtitle1">
								{label}
							</Typography>
						}
					/>
					{helperText && helperText}
					{errors[name] && <FormHelperText>{errors[name]?.message as string}</FormHelperText>}
				</FormControl>
			)}
		/>
	);
};
