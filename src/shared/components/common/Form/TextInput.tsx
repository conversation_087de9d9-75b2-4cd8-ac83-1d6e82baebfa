import {
	FormControl,
	FormHelperText,
	OutlinedInput,
	type OutlinedInputProps,
	Skeleton,
	Typography,
} from "@mui/material";
import type React from "react";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";

interface TextInputProps extends OutlinedInputProps {
	name: string;
	label: string;
	placeholder?: string;
	helperText?: React.ReactNode;
	isLoading?: boolean;
}

export const TextInput: React.FC<TextInputProps> = ({
	name,
	label,
	placeholder,
	helperText,
	isLoading = false,
	...props
}) => {
	const {
		control,
		formState: { errors },
	} = useFormContext();
	const id = useId();

	if (isLoading) {
		return (
			<FormControl fullWidth size="small" sx={{ mb: 2 }}>
				<Typography component="label" variant="subtitle1" mb={1}>
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
				{helperText && <Skeleton width="30%" height={20} sx={{ mt: 1 }} />}
			</FormControl>
		);
	}

	return (
		<Controller
			name={name}
			control={control}
			render={({ field }) => (
				<FormControl fullWidth size="small" error={!!errors[name]}>
					<Typography component="label" htmlFor={id} variant="subtitle1" mb={1}>
						{label}
					</Typography>
					<OutlinedInput
						{...field}
						{...props}
						id={id}
						placeholder={placeholder}
						aria-describedby={`${id}-helper`}
					/>
					{helperText && helperText}
					{errors[name] && (
						<FormHelperText id={`${id}-helper`}>
							{errors[name]?.message as string}
						</FormHelperText>
					)}
				</FormControl>
			)}
		/>
	);
};
