import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import type { ReactNode } from "react";
import {
	type DefaultValues,
	FormProvider as Provider,
	type SubmitHandler,
	useForm,
} from "react-hook-form";
import type * as z from "zod";

interface FormProviderProps<T extends z.ZodObject> {
	schema: T;
	defaultValues: DefaultValues<z.input<T>>
	onSubmit: SubmitHandler<z.infer<T>>;
	children: ReactNode;
}

export function FormProvider<T extends z.ZodObject>({
	schema,
	defaultValues,
	onSubmit,
	children,
}: FormProviderProps<T>) {
	const methods = useForm<z.input<typeof schema>, any, z.output<typeof schema>>(
		{
			resolver: zodResolver(schema),
			defaultValues,
		},
	);

	return (
		<Provider {...methods}>
			<form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>
		</Provider>
	);
}
