import {
	FormControl,
	FormHelperText,
	MenuItem,
	Select,
	type SelectProps,
	Skeleton,
	Typography,
} from "@mui/material";
import type React from "react";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { SelectOption } from "@/shared/types/common";

interface SelectInputProps extends Omit<SelectProps<any>, "name" | "value" | "onChange"> {
	name: string;
	label: string;
	options: SelectOption[];
	isLoading?: boolean;
	placeholder?: string;
	multiple?: boolean;
	helperText?: React.ReactNode;
	onValueChange?: (value: any) => any;
	filterOption?: (option: SelectOption, selected: any) => boolean;
}

export const SelectInput: React.FC<SelectInputProps> = ({
	name,
	label,
	placeholder = "Pilih opsi",
	options = [],
	isLoading = false,
	multiple = false,
	helperText,
	onValueChange,
	renderValue,
	filterOption,
	...props
}) => {
	const {
		control,
		formState: { errors },
	} = useFormContext();
	const id = useId();

	if (isLoading) {
		return (
			<FormControl fullWidth size="small" sx={{ mb: 2 }}>
				<Typography component="label" fontWeight={500} mb={1}>
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
				{helperText && <Skeleton width="30%" height={20} sx={{ mt: 1 }} />}
			</FormControl>
		);
	}

	return (
		<Controller
			name={name}
			control={control}
			render={({ field: { onChange, value } }) => (
				<FormControl fullWidth size="small" error={!!errors[name]}>
					<Typography variant="subtitle1" component="label" htmlFor={id} mb={1}>
						{label}
					</Typography>
					<Select
						{...props}
						id={id}
						multiple={multiple}
						displayEmpty
						value={value ?? (multiple ? [] : "")}
						onChange={(e) => {
							const raw = e.target.value;
							const finalValue = onValueChange ? onValueChange(raw) : raw;

							onChange(finalValue);
						}}
						renderValue={
							renderValue
								? renderValue
								: (selected) => {
										if (!selected || (Array.isArray(selected) && selected.length === 0)) {
											return placeholder;
										}

										if (multiple) {
											return (selected as string[])
												.map((v) => options.find((opt) => opt.value === v)?.label)
												.join(", ");
										}

										return options.find((opt) => opt.value === selected)?.label || placeholder;
									}
						}
					>
						<MenuItem disabled value="">
							{placeholder}
						</MenuItem>
						{options
							.filter((opt) => {
								if (!multiple) return true;

								if (typeof filterOption === "function") {
									return filterOption(opt, value);
								}

								// if you want the options not show when it's already selected
								// const selectedValues = Array.isArray(value)
								// 	? value.map((v) => (typeof v === "string" ? v : v?.value))
								// 	: [];

								// return !selectedValues.includes(opt.value);

								return true;
							})
							.map((opt) => (
								<MenuItem key={opt.value} value={opt.value}>
									{opt.label}
								</MenuItem>
							))}
					</Select>
					{helperText && helperText}
					{errors[name] && <FormHelperText>{errors[name]?.message as string}</FormHelperText>}
				</FormControl>
			)}
		/>
	);
};
