import {
	Autocomplete,
	type AutocompleteProps,
	FormControl,
	FormHelperText,
	Skeleton,
	TextField,
	Typography,
} from "@mui/material";
import type React from "react";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { SelectOption } from "@/shared/types/common";

interface AutocompleteInputProps {
	name: string;
	label: string;
	isLoading?: boolean;
	placeholder?: string;
	helperText?: React.ReactNode;
	renderInput?: AutocompleteProps<SelectOption, boolean, boolean, boolean>["renderInput"];

	/**
	 * Value mode untuk menentukan bagaimana data disimpan di form:
	 * - 'simple': Single value (string) atau array of strings
	 * - 'object': Custom object transformation menggunakan valueTransform
	 */
	valueMode?: "simple" | "object";

	/**
	 * Transform functions untuk mode 'object'
	 * - toFormValue: Convert dari option objects ke form value
	 * - toOptionValue: Convert dari form value ke option objects
	 */
	valueTransform?: {
		toFormValue: (selectedOptions: SelectOption[]) => any;
		toOptionValue: (formValue: any) => SelectOption[];
	};
}

export const AutocompleteSelectInput = <
	T extends SelectOption,
	Multiple extends boolean = false,
	DisableClearable extends boolean = false,
	FreeSolo extends boolean = false,
>({
	name,
	label,
	placeholder,
	isLoading = false,
	helperText,
	renderInput,
	valueMode = "simple",
	valueTransform,
	multiple,
	options,
	...props
}: AutocompleteInputProps &
	Omit<AutocompleteProps<T, Multiple, DisableClearable, FreeSolo>, "renderInput">) => {
	const {
		control,
		formState: { errors },
	} = useFormContext();
	const id = useId();

	if (isLoading) {
		return (
			<FormControl fullWidth size="small" sx={{ mb: 2 }}>
				<Typography component="label" fontWeight={500} mb={1}>
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
				{helperText && <Skeleton width="30%" height={20} sx={{ mt: 1 }} />}
			</FormControl>
		);
	}

	// Function untuk convert form value ke option objects
	const getDisplayValue = (formValue: any): SelectOption | SelectOption[] | null => {
		if (!formValue) {
			return multiple ? [] : null;
		}

		if (valueMode === "object" && valueTransform) {
			// Mode object: gunakan custom transformer
			return valueTransform.toOptionValue(formValue);
		}

		// Mode simple: cari option berdasarkan value
		if (multiple) {
			// Multiple simple: formValue adalah array of strings
			if (!Array.isArray(formValue)) return [];

			return formValue
				.map((val) => (options as unknown as SelectOption[]).find((opt) => opt.value === val))
				.filter(Boolean) as SelectOption[];
		}
		// Single simple: formValue adalah string
		return (options as unknown as SelectOption[]).find((opt) => opt.value === formValue) || null;
	};

	// Function untuk convert option objects ke form value
	const getFormValue = (selectedOptions: SelectOption | SelectOption[] | null): any => {
		if (!selectedOptions) {
			return multiple ? [] : "";
		}

		if (valueMode === "object" && valueTransform) {
			// Mode object: gunakan custom transformer
			const optionsArray = Array.isArray(selectedOptions) ? selectedOptions : [selectedOptions];
			return valueTransform.toFormValue(optionsArray);
		}

		// Mode simple: extract value dari option
		if (multiple) {
			// Multiple simple: return array of values
			const optionsArray = Array.isArray(selectedOptions) ? selectedOptions : [];
			return optionsArray.map((opt) => opt.value);
		}
		// Single simple: return single value
		return Array.isArray(selectedOptions)
			? selectedOptions[0]?.value || ""
			: selectedOptions.value || "";
	};

	return (
		<Controller
			name={name}
			control={control}
			render={({ field: { onChange, value } }) => (
				<FormControl fullWidth size="small" error={!!errors[name]}>
					<Typography variant="subtitle1" component="label" htmlFor={id} mb={1}>
						{label}
					</Typography>

					<Autocomplete
						{...props}
						multiple={multiple}
						options={options}
						id={id}
						value={getDisplayValue(value) as any}
						onChange={(_, newValue: any) => {
							const formValue = getFormValue(newValue);
							onChange(formValue);
						}}
						isOptionEqualToValue={(option, value) => option.value === value.value}
						renderInput={
							renderInput
								? renderInput
								: (params) => (
										<TextField
											{...params}
											placeholder={placeholder}
											size="small"
											variant="outlined"
											label=""
										/>
									)
						}
					/>
					{helperText && helperText}
					{errors[name] && <FormHelperText>{errors[name]?.message as string}</FormHelperText>}
				</FormControl>
			)}
		/>
	);
};
