import { Box } from "@mui/material";
import L from "leaflet";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	useMapEvents,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";

// Fix untuk marker icon di production
import icon from "leaflet/dist/images/marker-icon.png";
import iconRetina from "leaflet/dist/images/marker-icon-2x.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";
import type React from "react";

// Set default icon untuk Leaflet
const DefaultIcon = L.icon({
	iconUrl: icon,
	iconRetinaUrl: iconRetina,
	shadowUrl: iconShadow,
	iconSize: [25, 41],
	iconAnchor: [12, 41],
	popupAnchor: [1, -34],
	tooltipAnchor: [16, -28],
	shadowSize: [41, 41],
});

L.Marker.prototype.options.icon = DefaultIcon;

interface MapFieldProps {
	latitude: number;
	longitude: number;
	setLatitude: (lat: number) => void;
	setLongitude: (lng: number) => void;
	radius?: number;
	zoom?: number;
}

const LocationSelector: React.FC<{
	setLatitude: (lat: number) => void;
	setLongitude: (lng: number) => void;
}> = ({ setLatitude, setLongitude }) => {
	useMapEvents({
		click(e) {
			const { lat, lng } = e.latlng;
			setLatitude(lat);
			setLongitude(lng);
		},
	});
	return null;
};

export const MapField: React.FC<MapFieldProps> = ({
	latitude,
	longitude,
	setLatitude,
	setLongitude,
	radius = 30, // default radius
	zoom = 15, // default zoom level
}) => {
	return (
		<Box sx={{ height: 300, borderRadius: 2, overflow: "hidden" }}>
			<MapContainer
				center={[latitude, longitude]}
				zoom={zoom}
				scrollWheelZoom={true}
				style={{ height: "100%", width: "100%" }}
			>
				<TileLayer
					url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
					attribution='© <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors'
				/>
				<Marker position={[latitude, longitude]} />
				<Circle center={[latitude, longitude]} radius={radius} />
				<LocationSelector
					setLatitude={setLatitude}
					setLongitude={setLongitude}
				/>
			</MapContainer>
		</Box>
	);
};
