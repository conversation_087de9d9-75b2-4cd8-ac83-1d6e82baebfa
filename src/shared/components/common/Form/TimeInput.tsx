import {
	FormControl,
	FormHelperText,
	Skeleton,
	Typography,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import dayjs, { type Dayjs } from "dayjs";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";

interface TimeInputProps {
	name: string;
	label: string;
	placeholder?: string;
	helperText?: React.ReactNode;
	isLoading?: boolean;
	ampm?: boolean;
	disablePast?: boolean;
	minTime?: Dayjs;
	maxTime?: Dayjs;
}

export const TimeInput: React.FC<TimeInputProps> = ({
	name,
	label,
	placeholder,
	helperText,
	isLoading = false,
	ampm = false,
	disablePast,
	minTime,
	maxTime,
}) => {
	const {
		control,
		formState: { errors },
	} = useFormContext();
	const id = useId();

	if (isLoading) {
		return (
			<FormControl fullWidth size="small" sx={{ mb: 2 }}>
				<Typography component="label" variant="subtitle1" mb={1}>
					<Skeleton width="40%" />
				</Typography>
				<Skeleton variant="rounded" height={40} />
				{helperText && <Skeleton width="30%" height={20} sx={{ mt: 1 }} />}
			</FormControl>
		);
	}

	return (
		<LocalizationProvider dateAdapter={AdapterDayjs}>
			<Controller
				name={name}
				control={control}
				render={({ field: { onChange, value } }) => {
					// Value dari form bisa berupa string (e.g. "06:00:00")
					const parsedValue =
						typeof value === "string"
							? dayjs(value, ["HH:mm:ss"])
							: value;

					const timeValue = parsedValue?.isValid() ? parsedValue : null;

					return (
						<FormControl fullWidth size="small" error={!!errors[name]}>
							<Typography
								component="label"
								htmlFor={id}
								variant="subtitle1"
								mb={1}
							>
								{label}
							</Typography>
							<TimePicker
								value={timeValue}
								onChange={(val) =>
									onChange(val ? val.format("HH:mm:ss") : null)
								}
								ampm={ampm}
								disablePast={disablePast}
								minTime={minTime}
								maxTime={maxTime}
								format="HH:mm:ss"
								slotProps={{
									textField: {
										fullWidth: true,
										size: "small",
										placeholder,
									},
								}}
							/>
							{helperText && helperText}
							{errors[name] && (
								<FormHelperText>
									{errors[name]?.message as string}
								</FormHelperText>
							)}
						</FormControl>
					);
				}}
			/>
		</LocalizationProvider>
	);
};
