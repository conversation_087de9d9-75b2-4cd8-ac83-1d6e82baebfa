import { TableChart } from "@mui/icons-material";
import {
	Checkbox,
	Divider,
	IconButton,
	ListItemText,
	Menu,
	MenuItem,
	Tooltip,
	Typography,
} from "@mui/material";
import type { Column } from "@tanstack/react-table";
import { type MouseEvent, useState } from "react";

interface ViewOptionsProps<TData> {
	hideableColumns: Column<TData>[];
}

export function ViewOptions<TData>({
	hideableColumns,
}: ViewOptionsProps<TData>) {
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const open = Boolean(anchorEl);

	const handleOpen = (event: MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	return (
		<>
			<Tooltip title="Toggle columns">
				<IconButton
					// size="large"
					onClick={handleOpen}
					color="inherit"
					sx={{
						border: "1px solid",
						borderRadius: 1,
						backgroundColor: "transparent",
						"&:hover": {
							backgroundColor: "action.hover", // konsisten dengan outlined hover
						},
					}}
				>
					<TableChart />
				</IconButton>
			</Tooltip>
			<Menu
				anchorEl={anchorEl}
				open={open}
				onClose={handleClose}
				keepMounted
				sx={{ px: 0 }}
			>
				<Typography variant="subtitle2" sx={{ px: 2, pb: 1, fontWeight: 600 }}>
					Toggle Columns
				</Typography>
				<Divider />

				{hideableColumns.map((column) => {
					const label =
						(column?.columnDef?.meta as { columnLabel?: string })
							?.columnLabel ?? "";

					return (
						<MenuItem
							key={column.id}
							onClick={() => column.toggleVisibility()}
							sx={{ px: 2 }}
						>
							<Checkbox
								checked={column.getIsVisible()}
								onChange={(e) => column.toggleVisibility(e.target.checked)}
							/>
							<ListItemText
								primary={label}
								style={{ textTransform: "capitalize" }}
								sx={{ color: "text.primary" }}
							/>
						</MenuItem>
					);
				})}
			</Menu>
		</>
	);
}
