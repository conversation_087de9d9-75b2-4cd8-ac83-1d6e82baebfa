import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import {
	Box,
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogContentText,
	DialogTitle,
	Drawer,
	IconButton,
	Stack,
	type SxProps,
	Tooltip,
	Typography,
} from "@mui/material";
import type { Row } from "@tanstack/react-table";
import { useState } from "react";
import IconsaxEye2Icon from "@/assets/icons/iconsax-eye-2.svg?react";
import IconsaxPencilIcon from "@/assets/icons/iconsax-pencil.svg?react";
import IconsaxTrashIcon from "@/assets/icons/iconsax-trash.svg?react";
import { IconWrapper } from "@/shared/components/common/IconWrapper";

type RowActionsProps<T> = {
	row: Row<T>;
	onView?: (data: T) => void;
	onEdit?: (data: T) => void;
	onDelete?: (data: T) => void;
	renderDetail?: (data: T) => React.ReactNode;
	viewTitle?: string;
	deleteTitle?: string;
	deleteDescription?: (data: T) => string;
	drawerSx?: SxProps;
};

export function RowActions<T>({
	row,
	onView,
	onEdit,
	onDelete,
	renderDetail,
	viewTitle = "Detail Data",
	deleteTitle = "Hapus Data",
	deleteDescription = () => "Apakah Anda yakin ingin menghapus data ini?",
	drawerSx,
}: RowActionsProps<T>) {
	const [openDrawer, setOpenDrawer] = useState(false);
	const [openDialog, setOpenDialog] = useState(false);
	const data = row.original;

	const handleView = () => {
		if (onView) {
			onView(data);
		} else {
			setOpenDrawer(true);
		}
	};

	const handleEdit = () => onEdit?.(data);
	const handleDelete = () => setOpenDialog(true);
	const confirmDelete = () => {
		onDelete?.(data);
		setOpenDialog(false);
	};

	return (
		<>
			{/* Row Action Buttons */}
			<Stack direction="row" alignItems="center" flexWrap="nowrap">
				<Tooltip title="Lihat">
					<IconButton onClick={handleView}>
						<IconWrapper icon={IconsaxEye2Icon} />
					</IconButton>
				</Tooltip>

				{onEdit && (
					<Tooltip title="Edit">
						<IconButton color="secondary" onClick={handleEdit}>
							<IconWrapper icon={IconsaxPencilIcon} />
						</IconButton>
					</Tooltip>
				)}

				{onDelete && (
					<Tooltip title="Hapus">
						<IconButton color="error" onClick={handleDelete}>
							<IconWrapper icon={IconsaxTrashIcon} />
						</IconButton>
					</Tooltip>
				)}
			</Stack>

			{/* Detail Row Drawer */}
			<Drawer
				open={openDrawer}
				onClose={() => setOpenDrawer(false)}
				anchor="right"
				sx={{ zIndex: (theme) => theme.zIndex.drawer + 10 }}
			>
				<Box
					sx={{
						p: 4,
						minWidth: { xs: 375, sm: 500 },
						maxWidth: 800,
						...drawerSx,
					}}
				>
					<Stack direction="row" spacing={2} alignItems="center">
						<IconButton onClick={() => setOpenDrawer(false)}>
							<KeyboardDoubleArrowRightIcon />
						</IconButton>
						<Typography variant="subtitle1">{viewTitle}</Typography>
					</Stack>
					<Box sx={{ mt: 2 }}>
						{renderDetail ? (
							renderDetail(data)
						) : (
							<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
								{Object.entries(data as Record<string, unknown>).map(
									([key, value]) => (
										<Stack
											key={key}
											direction="row"
											justifyContent="space-between"
										>
											<Typography
												color="textDisabled"
												sx={{ textTransform: "capitalize" }}
											>
												{key}
											</Typography>
											<Typography>{String(value)}</Typography>
										</Stack>
									),
								)}
							</Stack>
						)}
					</Box>
				</Box>
			</Drawer>

			{/* Delete Row Confirmation Dialog */}
			<Dialog
				open={openDialog}
				onClose={() => setOpenDialog(false)}
				sx={{ p: 4 }}
			>
				<DialogTitle>{deleteTitle}</DialogTitle>
				<DialogContent>
					<DialogContentText>{deleteDescription(data)}</DialogContentText>
				</DialogContent>
				<DialogActions>
					<Button onClick={() => setOpenDialog(false)} color="inherit">
						Batal
					</Button>
					<Button onClick={confirmDelete} color="error" variant="contained">
						Hapus
					</Button>
				</DialogActions>
			</Dialog>
		</>
	);
}
