import { Box } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import type { PickerValue } from "@mui/x-date-pickers/internals";
import { toDateOnlyString } from "@/shared/utils/common";

export const DateRangeFilterInput: React.FC<{
	setRangeFilter: (minValue: any, maxValue: any, columnId: string) => void;
	columnId: string;
	start: PickerValue | null;
	end: PickerValue | null;
}> = ({ setRangeFilter, columnId, start, end }) => {
	const handleStartChange = (date: PickerValue) => {
		if (!date) return;
		const startValue = toDateOnlyString(date?.toDate()) ?? null;
		const endValue = end ? toDateOnlyString(end?.toDate()) : null;
		setRangeFilter(startValue, endValue, columnId);
	};

	const handleEndChange = (date: PickerValue) => {
		if (!date) return;
		const startValue = start ? toDateOnlyString(start?.toDate()) : null;
		const endValue = toDateOnlyString(date?.toDate()) ?? null;
		setRangeFilter(startValue, endValue, columnId);
	};

	return (
		<LocalizationProvider dateAdapter={AdapterDayjs}>
			<Box sx={{ display: "flex", gap: "4px", alignItems: "center", maxWidth: "350px" }}>
				<DatePicker
					label="From"
					value={start}
					onChange={handleStartChange}
					slotProps={{
						textField: { size: "small" },
					}}
				/>
				<DatePicker
					label="To"
					value={end}
					onChange={handleEndChange}
					slotProps={{
						textField: { size: "small" },
					}}
				/>
			</Box>
		</LocalizationProvider>
	);
};
