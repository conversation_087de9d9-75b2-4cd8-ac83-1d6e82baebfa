import { InputAdornment, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import IconsaxSearchIcon from "@/assets/icons/iconsax-search.svg?react";
import { IconWrapper } from "../IconWrapper";

export const TextFilterInput: React.FC<{
	currentValue: string;
	setFilter: (value: any, operator?: string) => void;
}> = ({ currentValue, setFilter }) => {
	const [inputValue, setInputValue] = useState(currentValue);

	const debouncedSetFilter = useDebouncedCallback((val: string) => {
		setFilter(val);
	}, 500);

	useEffect(() => {
		setInputValue(currentValue ?? "");
	}, [currentValue]);

	return (
		<TextField
			variant="outlined"
			size="small"
			value={inputValue}
			onChange={(e) => {
				const val = e.target.value;
				setInputValue(val);
				debouncedSetFilter(val);
			}}
			placeholder="Search..."
			sx={{ minWidth: 150 }}
			slotProps={{
				input: {
					startAdornment: (
						<InputAdornment position="start">
							<IconWrapper icon={IconsaxSearchIcon} />
						</InputAdornment>
					),
				},
			}}
		/>
	);
};
