import { Stack, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";

export const NumberRangeFilterInput: React.FC<{
	min: number | null;
	max: number | null;
	setRangeFilter: (minValue: any, maxValue: any, columnId: string) => void;
	columnId: string;
}> = ({ min, max, setRangeFilter, columnId }) => {
	const [localMin, setLocalMin] = useState<string>(min?.toString() ?? "");
	const [localMax, setLocalMax] = useState<string>(max?.toString() ?? "");

	// Update local state ketika parent berubah (misalnya clear filter)
	useEffect(() => {
		setLocalMin(min?.toString() ?? "");
	}, [min]);

	useEffect(() => {
		setLocalMax(max?.toString() ?? "");
	}, [max]);

	// Debounced setter
	const debouncedSetRange = useDebouncedCallback(
		(minStr: string, maxStr: string) => {
			const parsedMin = minStr === "" ? null : minStr;
			const parsedMax = maxStr === "" ? null : maxStr;
			// console.log(parsedMin, parsedMax, "parsed");
			setRangeFilter(parsedMin, parsedMax, columnId);
		},
		500,
	);

	const handleMinChange = (val: string) => {
		setLocalMin(val);
		debouncedSetRange(val, localMax);
	};

	const handleMaxChange = (val: string) => {
		setLocalMax(val);
		debouncedSetRange(localMin, val);
	};

	return (
		<Stack direction="row" spacing={2}>
			<TextField
				type="number"
				size="small"
				variant="outlined"
				value={localMin}
				onChange={(e) => handleMinChange(e.target.value)}
				placeholder="Min"
			/>
			<TextField
				type="number"
				size="small"
				variant="outlined"
				value={localMax}
				onChange={(e) => handleMaxChange(e.target.value)}
				placeholder="Max"
			/>
		</Stack>
	);
};
