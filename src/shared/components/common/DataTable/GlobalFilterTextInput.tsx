import { InputAdornment, TextField } from "@mui/material";
import { useEffect, useState } from "react"; // Tambahkan useEffect di sini
import { useDebouncedCallback } from "use-debounce";
import IconsaxSearchIcon from "@/assets/icons/iconsax-search.svg?react";
import { IconWrapper } from "../IconWrapper";

export const GlobalFilterTextInput: React.FC<{
	globalFilterValue: string; // Ini adalah globalFilter dari DataTable
	setGlobalFilterValue: (value: string) => void; // Ini adalah setGlobalFilter dari DataTable
}> = ({ globalFilterValue, setGlobalFilterValue }) => {
	const [inputValue, setInputValue] = useState(globalFilterValue);

	const debouncedSetGlobalFilter = useDebouncedCallback((val: string) => {
		setGlobalFilterValue(val);
	}, 500);

	useEffect(() => {
		setInputValue(globalFilterValue);
	}, [globalFilterValue]);

	return (
		<TextField
			variant="outlined"
			size="small"
			value={inputValue}
			onChange={(e) => {
				setInputValue(e.target.value);
				debouncedSetGlobalFilter(e.target.value);
			}}
			placeholder="Search..."
			sx={{ minWidth: 150 }}
			slotProps={{
				input: {
					startAdornment: (
						<InputAdornment position="start">
							<IconWrapper icon={IconsaxSearchIcon} />
						</InputAdornment>
					),
				},
			}}
		/>
	);
};
