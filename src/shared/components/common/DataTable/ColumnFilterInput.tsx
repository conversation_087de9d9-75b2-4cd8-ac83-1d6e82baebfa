import type { Column, Table } from "@tanstack/react-table";
import dayjs from "dayjs";
import {
	type FilterVariant,
	validOperatorsByVariant,
} from "@/shared/constants/dataTable";
import type {
	CustomColumnFiltersState,
	SelectOption,
} from "@/shared/types/common";
import { DateRangeFilterInput } from "./DateRangeFilterInput";
import { NumberRangeFilterInput } from "./NumberRangeFilterInput";
import { SelectFilterInput } from "./SelectFilterInput";
import { TextFilterInput } from "./TextFilterInput";

type ColumnFilterInputProps<TData> = {
	column: Column<TData, unknown>;
	table: Table<TData>;
};

const isValidFilterValue = (value: any) => {
	if (value === null || value === undefined) return false;
	if (typeof value === "string" && value.trim() === "") return false;
	if (Array.isArray(value) && value.length === 0) return false;
	return true;
};

export function ColumnFilterInput<TData>({
	column,
	table,
}: ColumnFilterInputProps<TData>) {
	const meta = column.columnDef.meta as {
		filterVariant?: FilterVariant;
		selectOptions?: SelectOption[];
		isMultipleSelect?: boolean;
	};

	const filterVariant = meta?.filterVariant ?? "text";

	const setFilter = (value: any, operator?: string) => {
		if (!isValidFilterValue(value)) {
			column.setFilterValue(undefined); // hapus filter jika invalid
			return;
		}

		column.setFilterValue({
			value,
			operator: operator ?? validOperatorsByVariant[filterVariant][0],
		});
	};

	// Fungsi khusus untuk range filters yang menangani dua filter terpisah
	const setRangeFilter = (minValue: any, maxValue: any, columnId: string) => {
		// Dapatkan table instance untuk mengakses setColumnFilters
		const currentFilters = table.getState().columnFilters;

		// Hapus filter existing untuk column ini
		const otherFilters = currentFilters.filter(
			(filter) => filter.id !== columnId,
		);

		const newFilters = [...otherFilters];

		// Tambahkan filter gte jika minValue ada
		if (minValue !== null && minValue !== undefined && minValue !== "") {
			newFilters.push({
				id: columnId,
				value: { value: minValue, operator: "gte" },
			});
		}

		// Tambahkan filter lte jika maxValue ada
		if (maxValue !== null && maxValue !== undefined && maxValue !== "") {
			newFilters.push({
				id: columnId,
				value: { value: maxValue, operator: "lte" },
			});
		}

		table.setColumnFilters(newFilters);
	};

	const filterValue = column.getFilterValue() as
		| { value: any; operator: string }
		| undefined;
	const currentValue = filterValue?.value;

	// 🔤 text / textSearch
	if (filterVariant === "text" || filterVariant === "textSearch") {
		return (
			<TextFilterInput
				currentValue={currentValue ?? ""}
				setFilter={setFilter}
			/>
		);
	}

	// 🔽 select
	if (filterVariant === "select") {
		const isMultiple = meta?.isMultipleSelect ?? false;
		const options = meta?.selectOptions ?? [];

		return (
			<SelectFilterInput
				currentValue={currentValue ?? (isMultiple ? [] : "")}
				setFilter={setFilter}
				isMultiple={isMultiple}
				options={options}
			/>
		);
	}

	// 📆 dateRange
	if (filterVariant === "dateRange") {
		// Dapatkan nilai min dan max dari filters yang ada
		// const table = column.table;
		const currentFilters = table.getState()
			.columnFilters as CustomColumnFiltersState;
		const columnFilters = currentFilters.filter(
			(filter) => filter.id === column.id,
		);

		const gteFilter = columnFilters.find((f) => f.value.operator === "gte");
		const lteFilter = columnFilters.find((f) => f.value.operator === "lte");

		const start = gteFilter?.value.value
			? dayjs(gteFilter.value.value as string)
			: null;
		const end = lteFilter?.value.value
			? dayjs(lteFilter.value.value as string)
			: null;

		return (
			<DateRangeFilterInput
				setRangeFilter={setRangeFilter}
				columnId={column.id}
				start={start}
				end={end}
			/>
		);
	}

	// 🔢 numberRange
	if (filterVariant === "numberRange") {
		// Dapatkan nilai min dan max dari filters yang ada
		// const table = column.table;
		const currentFilters = table.getState()
			.columnFilters as CustomColumnFiltersState;
		const columnFilters = currentFilters.filter(
			(filter) => filter.id === column.id,
		);

		const gteFilter = columnFilters.find((f) => f.value.operator === "gte");
		const lteFilter = columnFilters.find((f) => f.value.operator === "lte");

		const min = (gteFilter?.value.value as number) ?? null;
		const max = (lteFilter?.value.value as number) ?? null;

		return (
			<NumberRangeFilterInput
				min={min}
				max={max}
				setRangeFilter={setRangeFilter}
				columnId={column.id}
			/>
		);
	}

	return null;
}
