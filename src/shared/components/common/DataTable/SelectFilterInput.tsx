import { MenuItem, TextField } from "@mui/material";
import type { SelectOption } from "@/shared/types/common";

export const SelectFilterInput: React.FC<{
	currentValue: string | string[];
	setFilter: (value: any, operator?: string) => void;
	isMultiple: boolean;
	options: SelectOption[];
}> = ({ currentValue, setFilter, isMultiple, options }) => {
	return (
		<TextField
			select
			variant="outlined"
			size="small"
			label="Select"
			sx={{ minWidth: 120 }}
			value={currentValue}
			onChange={(e) => {
				setFilter(e.target.value, isMultiple ? "in" : "eq");
			}}
			slotProps={{ select: { multiple: isMultiple } }}
		>
			{options.map((opt) => (
				<MenuItem key={opt.value.toString()} value={opt.value}>
					{opt.label}
				</MenuItem>
			))}
		</TextField>
	);
};
