import { ArrowDropDown, ArrowDropUp, UnfoldMore, VisibilityOff } from "@mui/icons-material";
import { Box, Divider, Menu, MenuItem, type SxProps, Typography } from "@mui/material";
import type { Column } from "@tanstack/react-table";
import React from "react";

interface ColumnHeaderProps<TData, TValue> {
	column: Column<TData, TValue>;
	title: string;
	sx?: SxProps;
}

export function ColumnHeader<TData, TValue>({
	column,
	title,
	sx,
}: ColumnHeaderProps<TData, TValue>) {
	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
	const open = Boolean(anchorEl);

	const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget);
	};
	const handleClose = () => {
		setAnchorEl(null);
	};

	if (!column.getCanSort()) {
		return <Box sx={sx}>{title}</Box>;
	}

	const isSorted = column.getIsSorted();

	const sortIcon =
		isSorted === "asc" ? (
			<ArrowDropUp fontSize="small" />
		) : isSorted === "desc" ? (
			<ArrowDropDown fontSize="small" />
		) : (
			<UnfoldMore fontSize="small" />
		);

	return (
		<Box display="flex" alignItems="center">
			<Box
				component="button"
				onClick={handleOpen}
				sx={{
					display: "flex",
					alignItems: "center",
					gap: 1,
					py: 1.5,
					border: "none",
					outline: "none",
					backgroundColor: "transparent",
					cursor: "pointer",
					...sx,
				}}
			>
				<Typography variant="body2" sx={{ fontWeight: 700 }} noWrap>
					{title}
				</Typography>
				{sortIcon}
			</Box>

			<Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
				<MenuItem
					onClick={() => {
						column.toggleSorting(false, true);
						handleClose();
					}}
				>
					<ArrowDropUp fontSize="small" sx={{ mr: 1 }} />
					Sort Ascending
				</MenuItem>
				<MenuItem
					onClick={() => {
						column.toggleSorting(true, true);
						handleClose();
					}}
				>
					<ArrowDropDown fontSize="small" sx={{ mr: 1 }} />
					Sort Descending
				</MenuItem>

				<Divider />

				<MenuItem
					onClick={() => {
						column.toggleVisibility(false);
						handleClose();
					}}
				>
					<VisibilityOff fontSize="small" sx={{ mr: 1 }} />
					Hide Column
				</MenuItem>
			</Menu>
		</Box>
	);
}
