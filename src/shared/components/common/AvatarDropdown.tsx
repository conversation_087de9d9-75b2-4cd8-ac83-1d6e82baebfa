import AccountCircle from "@mui/icons-material/AccountCircle";
import Logout from "@mui/icons-material/Logout";
import { Avatar, Box, Divider, IconButton, ListItemIcon, Menu, MenuItem, Typography } from "@mui/material";
import type React from "react";
import { useState } from "react";
import type { UserSession } from "@/shared/types/api";

const stringToInitials = (name?: string) => {
	return name?.trim().charAt(0).toUpperCase() || "?";
};

export const AvatarDropdown: React.FC<{ userSession: UserSession; onLogout: () => void; onProfile: () => void }> = ({
	userSession,
	onLogout,
	onProfile,
}) => {
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const open = Boolean(anchorEl);

	const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	return (
		<>
			<IconButton onClick={handleOpen} size="small" sx={{ ml: 1 }}>
				<Avatar src={userSession?.image} alt={userSession?.name} sx={{ width: 40, height: 40 }}>
					{stringToInitials(userSession?.name)}
				</Avatar>
			</IconButton>
			<Menu
				anchorEl={anchorEl}
				open={open}
				onClose={handleClose}
				onClick={handleClose}
				slotProps={{
					paper: {
						elevation: 4,
						sx: {
							mt: 1.5,
							overflow: "visible",
							minWidth: 220,
							filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.1))",
							"& .MuiAvatar-root": {
								width: 32,
								height: 32,
								ml: -0.5,
								mr: 1,
							},
							"&:before": {
								content: '""',
								display: "block",
								position: "absolute",
								top: 0,
								right: 14,
								width: 10,
								height: 10,
								bgcolor: "background.paper",
								transform: "translateY(-50%) rotate(45deg)",
								zIndex: 0,
							},
						},
					},
				}}
				anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
				transformOrigin={{ vertical: "top", horizontal: "right" }}
			>
				<Box px={2} py={1}>
					<Typography variant="subtitle1">{userSession?.name}</Typography>
					<Typography variant="body2" color="text.disabled" noWrap>
						{userSession?.email}
					</Typography>
				</Box>
				<Divider />
				<MenuItem onClick={onProfile}>
					<ListItemIcon>
						<AccountCircle fontSize="small" />
					</ListItemIcon>
					Profil Saya
				</MenuItem>
				<MenuItem onClick={onLogout}>
					<ListItemIcon>
						<Logout fontSize="small" />
					</ListItemIcon>
					Logout
				</MenuItem>
			</Menu>
		</>
	);
};
