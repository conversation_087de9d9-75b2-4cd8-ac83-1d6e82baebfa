import { Box, Link, Typography } from "@mui/material";
import type React from "react";
import { Link as ReactRouterLink } from "react-router";
import GearLogo from "@/assets/logo.webp";

interface LogoProps {
	showTagline?: boolean;
	linkTo?: string;
}

export const Logo: React.FC<LogoProps> = ({ showTagline = true, linkTo = "/" }) => {
	return (
		<Link
			component={ReactRouterLink}
			to={linkTo}
			underline="none"
			sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
		>
			<Box component="img" src={GearLogo} alt="Logo Gear" sx={{ width: 30, height: 30, mr: 1, objectFit: "cover" }} />
			<Box sx={{ lineHeight: 1 }}>
				<Typography variant="body2" sx={{ fontWeight: 700, color: "text.primary", lineHeight: 1 }}>
					PT. Prima Multi Cipta Karya
				</Typography>
				{showTagline && (
					<Typography variant="caption" sx={{ color: "text.disabled", lineHeight: 1 }}>
						<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON>
					</Typography>
				)}
			</Box>
		</Link>
	);
};
