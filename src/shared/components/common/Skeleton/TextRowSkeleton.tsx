/** biome-ignore-all lint/suspicious/noArrayIndexKey: " */
import { Skeleton, Stack } from "@mui/material";
import { useId } from "react";

interface TextRowSkeletonProps {
	lines?: number;
	variant?: "title" | "body";
}

export const TextRowSkeleton: React.FC<TextRowSkeletonProps> = ({
	lines = 2,
	variant = "body",
}) => {
  const id = useId();
	const height = variant === "title" ? 28 : 20;
	return (
		<Stack spacing={1}>
			{Array.from({ length: lines }).map((_, index) => (
				<Skeleton key={`${id}-${index}`} height={height} />
			))}
		</Stack>
	);
};
