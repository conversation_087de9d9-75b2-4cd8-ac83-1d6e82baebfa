import { Skeleton, type SkeletonProps } from "@mui/material";

interface ButtonSkeletonProps extends SkeletonProps {
	width?: number;
	size?: "small" | "medium" | "large";
}

const sizeMap = {
	small: 32,
	medium: 36,
	large: 44,
};

export const ButtonSkeleton: React.FC<ButtonSkeletonProps> = ({
	width = 96,
	size = "medium",
  sx,
	...props
}) => (
	<Skeleton
		variant="rounded"
		sx={{
			width,
			height: sizeMap[size],
			...sx,
		}}
		{...props}
	/>
);
