import { Box, type SxProps, type Theme } from "@mui/material";
import type React from "react";

interface IconWrapperProps {
	icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
	size?: number | string;
	color?: string;
	sx?: SxProps<Theme>;
}

export const IconWrapper: React.FC<IconWrapperProps> = ({
	icon: Icon,
	size = 24,
	color = "inherit",
	sx,
	...rest
}) => {
	return (
		<Box
			component="span"
			sx={{
				display: "inline-flex",
				alignItems: "center",
				justifyContent: "center",
				color,
				width: size,
				height: size,
				...sx,
			}}
		>
			<Icon width="100%" height="100%" {...rest} />
		</Box>
	);
};
