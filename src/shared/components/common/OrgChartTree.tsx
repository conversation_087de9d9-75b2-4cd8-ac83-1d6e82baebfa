import ZoomInIcon from "@mui/icons-material/ZoomIn";
import ZoomOutIcon from "@mui/icons-material/ZoomOut";
import {
	Box,
	Card,
	CardContent,
	IconButton,
	Stack,
	Typography,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import Tree from "react-d3-tree";
import type { OrgChartNode } from "@/shared/types/common";

interface OrgChartTreeProps {
	data: OrgChartNode[];
}

const renderCustomNode = ({ nodeDatum }: { nodeDatum: OrgChartNode }) => {
	const attributesCount = Object.entries(nodeDatum?.attributes || {}).length;
	const lineHeight = 20; // tinggi per baris teks
	const baseHeight = 40; // tinggi minimum untuk nama
	const dynamicHeight = baseHeight + attributesCount * lineHeight;

	const width = 200;
	const height = dynamicHeight;
	const x = -width / 2;
	const y = -height / 2;

	return (
		<foreignObject width={width} height={height} x={x} y={y}>
			<Card
				variant="outlined"
				sx={{
					textAlign: "center",
					p: 1,
					display: "flex",
					flexDirection: "column",
					alignItems: "center",
					justifyContent: "center",
					width: "100%",
					height: "100%",
				}}
			>
				<CardContent sx={{ padding: "8px !important", width: "100%" }}>
					<Typography variant="subtitle2" noWrap>
						{nodeDatum.name}
					</Typography>
					<Stack direction="column">
						{Object.entries(nodeDatum?.attributes || {}).map(([key, value]) => (
							<Typography
								key={key}
								variant="caption"
								color="text.secondary"
								sx={{ whiteSpace: "nowrap" }}
							>
								{value}
							</Typography>
						))}
					</Stack>
				</CardContent>
			</Card>
		</foreignObject>
	);
};

export function OrgChartTree({ data }: OrgChartTreeProps) {
	const containerRef = useRef<HTMLDivElement>(null);
	const [translate, setTranslate] = useState({ x: 0, y: 0 });
	const [zoom, setZoom] = useState(0.8);

	useEffect(() => {
		if (containerRef.current) {
			const dimensions = containerRef.current.getBoundingClientRect();
			setTranslate({ x: dimensions.width / 2, y: 100 });
		}
	}, []);

	const handleZoomIn = () => setZoom((z) => Math.min(z + 0.2, 2));
	const handleZoomOut = () => setZoom((z) => Math.max(z - 0.2, 0.2));

	return (
		<Box position="relative" width="100%" height="600px">
			{/* Zoom controls */}
			<Box
				position="absolute"
				top={16}
				left={16}
				zIndex={10}
				bgcolor="white"
				borderRadius={1}
				boxShadow={2}
				display="flex"
				flexDirection="column"
			>
				<IconButton onClick={handleZoomIn}>
					<ZoomInIcon />
				</IconButton>
				<IconButton onClick={handleZoomOut}>
					<ZoomOutIcon />
				</IconButton>
			</Box>

			{/* Tree view */}
			<Box
				ref={containerRef}
				width="100%"
				height="100%"
				// sx={{ border: "1px solid" }}
			>
				<Tree
					data={data}
					translate={translate}
					zoom={zoom}
					renderCustomNodeElement={({ nodeDatum }) =>
						renderCustomNode({ nodeDatum: nodeDatum as any })
					}
					orientation="vertical"
					nodeSize={{ x: 200, y: 120 }}
					separation={{ siblings: 1.2, nonSiblings: 1.2 }}
				/>
			</Box>
		</Box>
	);
}
