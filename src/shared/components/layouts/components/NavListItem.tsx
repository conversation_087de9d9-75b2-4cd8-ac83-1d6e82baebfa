// import {
// 	ListItem as MuiListItem,
// 	ListItemButton as MuiListItemButton,
// 	ListItemIcon as MuiListItemIcon,
// 	ListItemText as MuiListItemText,
// } from "@mui/material";
// import { Link, useLocation } from "react-router";

// export const NavListItem: React.FC<{
// 	open: boolean;
// 	icon: React.ReactNode;
// 	text: string;
// 	path: string;
// }> = ({ open, icon, text, path }) => {
// 	const location = useLocation();
// 	return (
// 		<MuiListItem disablePadding sx={{ display: "block" }}>
// 			<MuiListItemButton
// 				sx={[
// 					{
// 						minHeight: 48,
// 						px: 2,
// 						color: "text.primary",
// 						borderRadius: "8px",
// 						"&.Mui-selected": {
// 							backgroundColor: "primary.main",
// 							color: "text.primary",
// 							fontWeight: 700,
// 						},
// 						"&.Mui-selected:hover": {
// 							backgroundColor: "primary.main",
// 						},
// 					},
// 					open
// 						? {
// 								justifyContent: "initial",
// 							}
// 						: {
// 								justifyContent: "center",
// 							},
// 				]}
// 				selected={location.pathname === path}
// 				component={Link}
// 				to={path}
// 			>
// 				<MuiListItemIcon
// 					sx={[
// 						{
// 							minWidth: 0,
// 							justifyContent: "center",
// 						},
// 						open
// 							? {
// 									mr: 2,
// 								}
// 							: {
// 									mr: "auto",
// 								},
// 					]}
// 				>
// 					{icon}
// 				</MuiListItemIcon>
// 				<MuiListItemText
// 					primary={text}
// 					sx={[
// 						open
// 							? {
// 									opacity: 1,
// 								}
// 							: {
// 									opacity: 0,
// 								},
// 					]}
// 				/>
// 			</MuiListItemButton>
// 		</MuiListItem>
// 	);
// };

import { ExpandLess, ExpandMore } from "@mui/icons-material";
import {
	alpha,
	Collapse,
	List as MuiList,
	ListItem as MuiListItem,
	ListItemButton as MuiListItemButton,
	ListItemIcon as MuiListItemIcon,
	ListItemText as MuiListItemText,
} from "@mui/material";
import { useState } from "react";
import { Link, useLocation } from "react-router";

interface NavItem {
	id: string;
	icon?: React.ReactNode;
	text: string;
	path: string;
	children?: NavItem[];
}

export const NavListItem: React.FC<{
	item: NavItem;
	open: boolean;
}> = ({ item, open }) => {
	const location = useLocation();
	const [expand, setExpand] = useState(() =>
		location.pathname.startsWith(item.path),
	);

	const isParentSelected = location.pathname.startsWith(item.path);
	const toggleExpand = () => setExpand((prev) => !prev);

	if (item.children?.length) {
		return (
			<>
				<MuiListItem disablePadding sx={{ display: "block" }}>
					<MuiListItemButton
						onClick={toggleExpand}
						selected={isParentSelected}
						sx={[
							{
								minHeight: 48,
								px: 2,
								color: "text.primary",
								borderRadius: "8px",
								"&.Mui-selected": {
									backgroundColor: "primary.main",
									fontWeight: 700,
								},
								"&.Mui-selected:hover": {
									backgroundColor: "primary.main",
								},
							},
							open
								? {
										justifyContent: "initial",
									}
								: {
										justifyContent: "center",
									},
						]}
					>
						{item.icon && (
							<MuiListItemIcon
								sx={[
									{ minWidth: 0, justifyContent: "center" },
									open ? { mr: 2 } : { mr: "auto" },
								]}
							>
								{item.icon}
							</MuiListItemIcon>
						)}
						<MuiListItemText
							primary={item.text}
							sx={{ opacity: open ? 1 : 0 }}
						/>
						{open && (expand ? <ExpandLess /> : <ExpandMore />)}
					</MuiListItemButton>
				</MuiListItem>

				<Collapse in={expand} timeout="auto" unmountOnExit>
					<MuiList disablePadding>
						{item.children.map((child) => {
							const isChildSelected = location.pathname === child.path;
							return (
								<MuiListItem disablePadding key={child.id}>
									<MuiListItemButton
										component={Link}
										to={child.path}
										selected={isChildSelected}
										sx={(theme) => ({
											pl: open ? 6 : 2,
											py: 1,
											borderRadius: 0,
											color: "text.primary",
											"&.Mui-selected": {
												backgroundColor: "transparent",
												borderBottom: "1px solid",
												borderBottomColor: alpha(theme.palette.primary.main, 1),
											},
											"&.Mui-selected:hover": {
												backgroundColor: "transparent",
											},
										})}
									>
										<MuiListItemText
											primary={child.text}
											sx={{ opacity: open ? 1 : 0 }}
										/>
									</MuiListItemButton>
								</MuiListItem>
							);
						})}
					</MuiList>
				</Collapse>
			</>
		);
	}

	// Fallback to leaf nav item
	return (
		<MuiListItem disablePadding sx={{ display: "block" }}>
			<MuiListItemButton
				component={Link}
				to={item.path}
				selected={location.pathname === item.path}
				sx={[
					{
						minHeight: 48,
						px: 2,
						color: "text.primary",
						borderRadius: "8px",
						"&.Mui-selected": {
							backgroundColor: "primary.main",
							fontWeight: 700,
						},
						"&.Mui-selected:hover": {
							backgroundColor: "primary.main",
						},
					},
					open
						? {
								justifyContent: "initial",
							}
						: {
								justifyContent: "center",
							},
				]}
			>
				{item.icon && (
					<MuiListItemIcon
						sx={[
							{ minWidth: 0, justifyContent: "center" },
							open ? { mr: 2 } : { mr: "auto" },
						]}
					>
						{item.icon}
					</MuiListItemIcon>
				)}
				<MuiListItemText primary={item.text} sx={{ opacity: open ? 1 : 0 }} />
			</MuiListItemButton>
		</MuiListItem>
	);
};
