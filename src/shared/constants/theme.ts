import { createTheme } from "@mui/material/styles";

const theme = createTheme({
	palette: {
		mode: "light",
		primary: {
			main: "#FFCA28",
		},
		secondary: {
			main: "#388E3C",
		},
		text: {
			primary: "#1A1A1A",
		},
	},
	typography: {
		fontFamily: "'Nunito', sans-serif",
		h1: {
			fontSize: "4rem", // 64px
			fontWeight: 600,
		},
		h2: {
			fontSize: "3rem", // 48px
			fontWeight: 600,
		},
		h3: {
			fontSize: "2.5rem", // 40px
			fontWeight: 600,
		},
		h4: {
			fontSize: "2.25rem", // 36px
			fontWeight: 600,
		},
		h5: {
			fontSize: "2rem", // 32px
			fontWeight: 600,
		},
		h6: {
			fontSize: "1.5rem", // 24px
			fontWeight: 600,
		},
		subtitle1: {
			fontSize: "1rem", // 16px
			fontWeight: 600,
		},
		subtitle2: {
			fontSize: "0.875rem", // 14px
			fontWeight: 600,
		},
		body1: {
			fontSize: "1rem", // 16px
			fontWeight: 400,
		},
		body2: {
			fontSize: "0.875rem", // 14px
			fontWeight: 400,
		},
		caption: {
			fontSize: "0.75rem", // 12px
			fontWeight: 400,
		},
		overline: {
			fontSize: "0.75rem", // 12px
			fontWeight: 600,
			letterSpacing: "1px",
			// textTransform: 'uppercase',
		},
		button: {
			fontSize: "1rem", // 16px
			fontWeight: 600,
			textTransform: "none", // Default button text transform
		},
	},
	shape: {
		borderRadius: 8, // Default border radius for components
	},
});

export default theme;
