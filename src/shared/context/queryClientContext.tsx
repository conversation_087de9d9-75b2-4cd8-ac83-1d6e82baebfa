import { MutationCache, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import type React from "react";
import { useToast } from "../hooks";

interface AppQueryClientProviderProps {
	children: React.ReactNode;
}

export const AppQueryClientProvider: React.FC<AppQueryClientProviderProps> = ({ children }) => {
	const { showToast } = useToast();

	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				refetchOnWindowFocus: false,
			},
		},
		mutationCache: new MutationCache({
			onSuccess: (data: any) => {
				if (data?.message) {
					showToast({ message: data.message, severity: "success" });
				}
			},
			// onError: (error) => {
			// 	if (error instanceof AxiosError) {
			// 		const message = error.response?.data?.message || error.message;
			// 		showToast({ message, severity: "error" });
			// 	}
			// },
		}),
	});

	return (
		<QueryClientProvider client={queryClient}>
			{children}
			<ReactQueryDevtools initialIsOpen={false} />
		</QueryClientProvider>
	);
};
