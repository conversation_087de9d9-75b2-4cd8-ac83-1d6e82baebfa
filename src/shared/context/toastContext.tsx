import { Alert, type AlertColor, Slide, Snackbar, type SnackbarOrigin, useTheme } from "@mui/material";
import { createContext, type FC, type ReactNode, useCallback, useState } from "react";

type ToastOptions = {
	message: string;
	severity?: AlertColor;
	anchorOrigin?: SnackbarOrigin;
};

export interface ToastContextType {
	showToast: (messageOrOptions: string | ToastOptions) => void;
}

const defaultAnchor: SnackbarOrigin = {
	vertical: "bottom",
	horizontal: "right",
};

export const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider: FC<{ children: ReactNode }> = ({ children }) => {
	const [open, setOpen] = useState(false);
	const [message, setMessage] = useState("");
	const [severity, setSeverity] = useState<AlertColor>("info");
	const [anchorOrigin, setAnchorOrigin] = useState<SnackbarOrigin>(defaultAnchor);
	const theme = useTheme();

	const handleClose = (_event?: React.SyntheticEvent | Event, reason?: string) => {
		if (reason === "clickaway") return;
		setOpen(false);
	};

	const showToast = useCallback((input: string | ToastOptions) => {
		if (typeof input === "string") {
			setMessage(input);
			setSeverity("info");
			setAnchorOrigin(defaultAnchor);
		} else {
			setMessage(input.message);
			setSeverity(input.severity ?? "info");
			setAnchorOrigin(input.anchorOrigin ?? defaultAnchor);
		}
		setOpen(true);
	}, []);

	const getColorBySeverity = useCallback(
		(severity: AlertColor = "info") => {
			switch (severity) {
				case "error":
					return theme.palette.error.main;
				case "warning":
					return theme.palette.warning.main;
				case "success":
					return theme.palette.success.main;
				// case "info":
				default:
					return theme.palette.info.main;
			}
		},
		[theme],
	);

	return (
		<ToastContext.Provider value={{ showToast }}>
			{children}
			<Snackbar
				open={open}
				autoHideDuration={5000}
				onClose={handleClose}
				anchorOrigin={anchorOrigin}
				slots={{ transition: Slide }}
			>
				<Alert
					onClose={handleClose}
					severity={severity}
					variant="outlined" // gunakan outlined agar background soft
					sx={{
						width: "100%",
						backgroundColor: "#F6F6F6",
						color: "#1D1F1F", // teks gelap
						border: "none", // hilangkan border asli
						boxShadow: "none", // hilangkan shadow
						"& .MuiAlert-icon": {
							color: getColorBySeverity(severity), // ikon disesuaikan warna status
						},
						borderLeft: `4px solid ${getColorBySeverity(severity)}`, // aksen kiri
					}}
				>
					{message}
				</Alert>
			</Snackbar>
		</ToastContext.Provider>
	);
};
