import {
	createContext,
	type ReactNode,
	useCallback,
	useEffect,
	useMemo,
	useState,
} from "react";
import type {
	GetMeResponse,
	LoginPayload,
	LoginResponse,
	LogoutResponse,
	UserSession,
} from "@/shared/types/api";
import { apiClient } from "../api/apiClient";

interface AuthContextValue {
	user: UserSession | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	login: (payload: LoginPayload) => Promise<LoginResponse>;
	logout: () => Promise<void>;
	checkAuthStatus: () => Promise<void>;
}

export interface AuthContextType extends AuthContextValue {}

export const AuthContext = createContext<AuthContextValue>({
	user: null,
	isAuthenticated: false,
	isLoading: false,
	login: async () => ({}) as LoginResponse,
	logout: async () => {},
	checkAuthStatus: async () => {},
});

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
	children,
}) => {
	const [user, setUser] = useState<UserSession | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isAuthenticated, setIsAuthenticated] = useState(false);

	useEffect(() => {
		checkAuthStatus();
	}, []);

	const checkAuthStatus = useCallback(async () => {
		try {
			const result = await apiClient.get<GetMeResponse>("/api/v1/auth/me");

			setUser(result.data);
			setIsAuthenticated(true);
		} catch {
			setUser(null);
			setIsAuthenticated(false);
		} finally {
			setIsLoading(false);
		}
	}, []);

	const login = useCallback(async (payload: LoginPayload) => {
		const result = await apiClient.post<LoginResponse>(
			"/api/v1/admin/auth/login",
			payload,
		);
		setUser(result.data.user);
		setIsAuthenticated(true);
		return result;
	}, []);

	const logout = useCallback(async () => {
		try {
			await apiClient.post<LogoutResponse>("/api/v1/auth/logout");
			setUser(null);
			setIsAuthenticated(false);
		} catch {
			setUser(null);
			setIsAuthenticated(false);
		}
	}, []);

	const contextValue = useMemo(
		() => ({
			user,
			isAuthenticated,
			isLoading,
			login,
			logout,
			checkAuthStatus,
		}),
		[user, isAuthenticated, isLoading, login, logout, checkAuthStatus],
	);

	return (
		<AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
	);
};
