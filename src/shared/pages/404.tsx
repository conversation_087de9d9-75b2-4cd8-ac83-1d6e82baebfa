import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { Box, Button, Typography } from "@mui/material";
import { useNavigate } from "react-router";

const NotFoundPage: React.FC<{
	title?: string;
	message?: string;
	description?: string;
	buttonText?: string;
	redirectTo?: string;
	resourceType?: string | null;
	resourceId?: string | null;
}> = ({
	title = "404",
	message = "Halaman tidak ditemukan",
	description = "Halaman yang kamu cari mungkin sudah dihapus, atau URL-nya salah. Silakan kembali ke halaman utama.",
	buttonText = "Kembali ke Beranda",
	redirectTo = "/",
	resourceType = null,
	resourceId = null,
}) => {
	const navigate = useNavigate();

	// Generate dynamic messages based on resource type
	const getResourceMessage = () => {
		if (!resourceType) return { message, description };

		// Capitalize first letter of resource type
		const capitalizedResource =
			resourceType.charAt(0).toUpperCase() + resourceType.slice(1);

		return {
			message: `${capitalizedResource} tidak ditemukan`,
			description: resourceId
				? `${capitalizedResource} dengan ID ${resourceId} tidak ditemukan atau mungkin sudah dihapus. Silakan coba dengan ID yang berbeda atau kembali ke daftar ${resourceType}.`
				: `${capitalizedResource} yang kamu cari tidak ditemukan atau mungkin sudah dihapus. Silakan kembali ke daftar ${resourceType}.`,
		};
	};

	const getButtonConfig = () => {
		if (!resourceType) return { text: buttonText, path: redirectTo };

		// Generate dynamic button text and path
		const capitalizedResource =
			resourceType.charAt(0).toUpperCase() + resourceType.slice(1);
		const pluralResource = resourceType.endsWith("y")
			? `${resourceType.slice(0, -1)}ies` // category -> categories
			: resourceType + "s"; // user -> users, product -> products

		return {
			text: `Kembali ke Daftar ${capitalizedResource}`,
			path: redirectTo || `/${pluralResource}`,
		};
	};

	const resourceMessage = getResourceMessage();
	const buttonConfig = getButtonConfig();

	return (
		<Box
			display="flex"
			flexDirection="column"
			alignItems="center"
			justifyContent="center"
			minHeight="100vh"
			textAlign="center"
			sx={{ px: 2 }}
		>
			<ErrorOutlineIcon sx={{ fontSize: 80, color: "primary.main", mb: 2 }} />
			<Typography variant="h1" fontWeight={700} color="text.primary">
				{title}
			</Typography>
			<Typography variant="h5" color="text.primary" mb={2}>
				{resourceMessage.message}
			</Typography>
			<Typography variant="body1" color="text.disabled" maxWidth="sm" mb={4}>
				{resourceMessage.description}
			</Typography>
			<Button
				variant="contained"
				color="primary"
				size="large"
				onClick={() => navigate(buttonConfig.path)}
				sx={{ textTransform: "none", fontWeight: 700 }}
			>
				{buttonConfig.text}
			</Button>
		</Box>
	);
};

export default NotFoundPage;
