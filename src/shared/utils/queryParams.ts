import type { SortingState } from "@tanstack/react-table";
import type { CustomColumnFiltersState } from "../types/common";

/**
 * Build query parameters dengan format operator:value
 * Contoh: ?status=eq:active&status=eq:pending&role=in:admin,user&createdAt=gte:2023-01-01&createdAt=lte:2023-12-31
 */
export function buildQueryParams({
	pageIndex,
	pageSize,
	columnFilters,
	sorting,
	globalFilter,
}: {
	pageIndex: number;
	pageSize: number;
	columnFilters: CustomColumnFiltersState;
	sorting: SortingState;
	globalFilter: string;
}) {
	const params = new URLSearchParams();

	// Add pagination
	params.append("pageIndex", pageIndex.toString());
	params.append("pageSize", pageSize.toString());

	// Add sorting
	if (sorting.length > 0) {
		params.append("sort", sorting.map((s) => `${s.id}:${s.desc ? "desc" : "asc"}`).join(","));
	}

	// Add global filter
	if (globalFilter && globalFilter.trim() !== "") {
		params.append("search", globalFilter.trim());
	}

	// Add filters dengan format operator:value
	for (const filter of columnFilters) {
		const { id, value } = filter;
		const { value: filterValue, operator } = value;

		// Skip empty values
		if (filterValue === null || filterValue === undefined || filterValue === "") {
			continue;
		}

		// Handle array values (untuk operator 'in')
		if (Array.isArray(filterValue)) {
			const arrayValue = filterValue
				.filter((v) => v !== null && v !== undefined && v !== "")
				.join(",");
			if (arrayValue) {
				params.append(id, `${operator}:${arrayValue}`);
			}
		} else {
			// Handle single values
			params.append(id, `${operator}:${filterValue}`);
		}
	}

	return params;
}

/**
 * Parse query parameters kembali ke state
 * Mengubah format operator:value kembali ke CustomColumnFiltersState
 */
export function parseQueryParams(searchParams: URLSearchParams | string): {
	pageIndex: number;
	pageSize: number;
	columnFilters: CustomColumnFiltersState;
	sorting: SortingState;
  globalFilter: string;
} {
	const params =
		typeof searchParams === "string" ? new URLSearchParams(searchParams) : searchParams;

	// Parse pagination
	const pageIndex = Number.parseInt(params.get("pageIndex") || "0", 10);
	const pageSize = Number.parseInt(params.get("pageSize") || "10", 10);

	// Parse sorting
	const sorting: SortingState = [];
	const sortParam = params.get("sort");
	if (sortParam) {
		const sortItems = sortParam.split(",");
		for (const item of sortItems) {
			const [id, direction] = item.split(":");
			if (id && direction) {
				sorting.push({
					id,
					desc: direction === "desc",
				});
			}
		}
	}

	// Parse filters
	const columnFilters: CustomColumnFiltersState = [];
	// const processedParams = new Set<string>();

	for (const [key, value] of params.entries()) {
		// Skip pagination and sorting parameters
		if (key === "pageIndex" || key === "pageSize" || key === "sort") {
			continue;
		}

		// Parse operator:value format
		const colonIndex = value.indexOf(":");
		if (colonIndex === -1) {
			continue; // Skip invalid format
		}

		const operator = value.substring(0, colonIndex);
		const filterValue = value.substring(colonIndex + 1);

		// Validate operator
		if (!["eq", "ilike", "in", "gte", "lte"].includes(operator)) {
			continue;
		}

		// Parse value based on operator
		let parsedValue: any;
		if (operator === "in") {
			parsedValue = filterValue.split(",").filter((v) => v !== "");
		} else if (operator === "gte" || operator === "lte") {
			// Try to parse as number for numeric comparisons
			const numValue = Number(filterValue);
			parsedValue = !Number.isNaN(numValue) ? numValue : filterValue;
		} else {
			parsedValue = filterValue;
		}

		columnFilters.push({
			id: key,
			value: {
				value: parsedValue,
				operator: operator as any,
			},
		});
	}

	const globalFilter = params.get("search") ?? "";

	return {
		pageIndex,
		pageSize,
		columnFilters,
		sorting,
    globalFilter,
	};
}

/**
 * Build query string dari state
 */
export function buildQueryString(state: {
	pageIndex: number;
	pageSize: number;
	columnFilters: CustomColumnFiltersState;
	sorting: SortingState;
  globalFilter: string,
}): string {
	const params = buildQueryParams(state);
	return params.toString();
}

/**
 * Build complete URL dengan query parameters
 */
export function buildFilteredUrl(
	baseUrl: string,
	state: {
		pageIndex: number;
		pageSize: number;
		columnFilters: CustomColumnFiltersState;
		sorting: SortingState;
    globalFilter: string;
	},
): string {
	const queryString = buildQueryString(state);
	const separator = baseUrl.includes("?") ? "&" : "?";
	return queryString ? `${baseUrl}${separator}${queryString}` : baseUrl;
}

/**
 * Helper function untuk membuat filter state yang mudah
 */
export function createFilter(
	id: string,
	value: string | string[] | number | [number, number] | [string, string],
	operator: "eq" | "ilike" | "in" | "gte" | "lte",
) {
	return {
		id,
		value: {
			value,
			operator,
		},
	};
}

/**
 * Helper function untuk membuat range filters (gte + lte)
 */
export function createRangeFilters(
	id: string,
	min: number | string | null,
	max: number | string | null,
) {
	const filters = [];

	if (min !== null && min !== undefined && min !== "") {
		filters.push(createFilter(id, min, "gte"));
	}

	if (max !== null && max !== undefined && max !== "") {
		filters.push(createFilter(id, max, "lte"));
	}

	return filters;
}
