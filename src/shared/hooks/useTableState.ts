import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { buildQueryParams, parseQueryParams } from "../utils/queryParams";

export const useTableState = () => {
	const [searchParams, setSearchParams] = useSearchParams();

	// Initialize state dari URL
	const [tableState, setTableState] = useState(() => {
		return parseQueryParams(searchParams);
	});

	// Update URL ketika state berubah
	useEffect(() => {
		const newParams = buildQueryParams(tableState);
		setSearchParams(newParams, { replace: true });
	}, [tableState, setSearchParams]);

	return [tableState, setTableState] as const;
};
