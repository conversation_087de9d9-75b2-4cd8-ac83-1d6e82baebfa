import type { UseMutationResult } from "@tanstack/react-query";
import { useCallback } from "react";
import type { BulkActionPayload } from "../types/common";
import { buildQueryParams } from "../utils/queryParams";

interface SelectionData {
	selectedIds: string[];
	excludedIds: string[];
	isSelectAll: boolean;
	selectionInfo: any;
	getCurrentFilters?: () => {
		sorting: any;
		columnFilters: any;
		globalFilter: string;
		pagination: { pageIndex: number; pageSize: number };
	};
}

type BulkActionType = "delete" | "export" | "update";

export function useBulkActionHandler<TData = unknown>(
	mutation: UseMutationResult<TData, unknown, BulkActionPayload, unknown>,
	selectionData: SelectionData,
) {
	const handleBulkAction = useCallback(
		(action: BulkActionType, onDeselectAll?: () => void) => {
			const payload: BulkActionPayload = {
				action,
				selection: {
					isSelectAll: selectionData.isSelectAll,
					selectedIds: selectionData.isSelectAll
						? undefined
						: selectionData.selectedIds,
					excludedIds: selectionData.isSelectAll
						? selectionData.excludedIds
						: undefined,
				},
			};

			const currentFilters = selectionData.getCurrentFilters?.();
			if (currentFilters) {
				payload.queryString = buildQueryParams({
					pageIndex: currentFilters.pagination.pageIndex,
					pageSize: currentFilters.pagination.pageSize,
					sorting: currentFilters.sorting,
					columnFilters: currentFilters.columnFilters,
					globalFilter: currentFilters.globalFilter,
				}).toString();
			}

			mutation.mutate(payload);

			onDeselectAll?.();
		},
		[mutation, selectionData],
	);

	return handleBulkAction;
}
