import { useNavigate } from "react-router-dom";
import { useToast } from "./useToast";

export const useMutationCallbacks = () => {
	const navigate = useNavigate();
	const { showToast } = useToast();

	const handleSuccess = (message: string, redirectPath?: string) => {
		showToast({
			message,
			severity: "success",
		});

		if (redirectPath) {
			setTimeout(() => {
				navigate(redirectPath, { replace: true });
			}, 500);
		}
	};

	const handleError = (error: any) => {
		showToast({
			message: error.response?.data?.message || "<PERSON><PERSON><PERSON><PERSON> k<PERSON>",
			severity: "error",
		});
	};

	return { handleSuccess, handleError };
};
