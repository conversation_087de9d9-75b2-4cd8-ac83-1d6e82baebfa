import { useIsFetching, useIsMutating } from "@tanstack/react-query";
import NProgress from "nprogress";
import { useEffect } from "react";

export const useGlobalLoader = (): void => {
	const isFetching = useIsFetching();
	const isMutating = useIsMutating();

	useEffect(() => {
		NProgress.configure({ showSpinner: false });
		if (isFetching > 0 || isMutating > 0) {
			NProgress.start();
		} else {
			NProgress.done();
		}
	}, [isFetching, isMutating]);
};
