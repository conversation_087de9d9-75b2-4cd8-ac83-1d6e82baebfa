import type { SortingState } from "@tanstack/react-table";
import type { components, paths } from "./schema";

export type ApiRequestBody<
	Path extends keyof paths,
	Method extends keyof paths[Path] &
		("get" | "post" | "put" | "patch" | "delete"),
> = paths[Path][Method] extends {
	requestBody?: { content: { "application/json": infer TBody } };
}
	? TBody
	: never;

type ExtractStatusCodes<T> = T extends { responses: infer R }
	? keyof R & number
	: 200;

export type ApiResponseBody<
	Path extends keyof paths,
	Method extends keyof paths[Path] &
		("get" | "post" | "put" | "patch" | "delete"),
	StatusCode extends number = ExtractStatusCodes<paths[Path][Method]>,
> = paths[Path][Method] extends {
	responses: {
		[K in StatusCode]: { content: { "application/json": infer TResponse } };
	};
}
	? TResponse
	: never;

export type Schema<T extends keyof components["schemas"]> =
	components["schemas"][T];

// Form
export interface SelectOption {
	label: string;
	value: string | number;
}

// Datatable
export type FilterOperator = "eq" | "ilike" | "in" | "gte" | "lte";

export interface CustomColumnFilterValue {
	value: string | string[] | number | [number, number] | [string, string];
	operator: FilterOperator;
}

export interface CustomColumnFilter {
	id: string;
	value: CustomColumnFilterValue;
}

export type CustomColumnFiltersState = CustomColumnFilter[];

export interface SelectionState {
	isSelectAll: boolean;
	selectedIds: Set<string>;
	excludedIds: Set<string>;
}

export interface SelectionInfo {
	selectedCount: number;
	totalCount: number;
	isSelectAll: boolean;
}

// Bulk Action
export interface BulkActionPayload {
	action: "delete" | "export" | "update";
	selection: {
		isSelectAll: boolean;
		selectedIds?: string[];
		excludedIds?: string[];
	};
	queryString?: string;
}

// Common
export interface NavItem {
	id: string;
	icon?: React.ReactNode;
	text: string;
	path: string;
	children?: NavItem[];
}

export interface OrgChartNode {
	name: string;
	attributes?: Record<string, string>;
	children?: OrgChartNode[];
}

export interface SelectionData {
	selectedIds: string[];
	excludedIds: string[];
	isSelectAll: boolean;
	selectionInfo: {
		selectedCount: number;
		totalCount: number;
		isSelectAll: boolean;
	};
	getCurrentFilters?: () => {
		sorting: SortingState;
		columnFilters: CustomColumnFiltersState;
		globalFilter: string;
		pagination: { pageIndex: number; pageSize: number };
	};
}
