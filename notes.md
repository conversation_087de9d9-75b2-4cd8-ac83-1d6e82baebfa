# 📸 Responsive Image Cheat Sheet

## ✅ Aspect Ratio & Use Cases

| Ratio   | Ukuran Umum        | Keterangan                             | Cocok untuk                        |
|---------|--------------------|----------------------------------------|------------------------------------|
| **1:1** | 800×800            | High quality square                    | Hero square, mobile slider         |
|         | 600×600            | Medium quality, lebih ringan           | Mobile-first slider                |
|         | 400×400            | Thumbnail optimal                      | Avatar, product thumbnail          |
| **4:3** | 1200×900           | Desktop medium                         | Gallery, blog images               |
|         | 800×600            | Mobile/tablet                          | Card image                         |
|         | 640×480            | Light image                            | Legacy support, preview            |
| **3:2** | 1200×800           | Cinematic, modern                      | Landing page, article header       |
|         | 900×600            | Tablet layout                          | Product promo                      |
| **16:9**| 1920×1080          | Full HD, hero banner                   | Fullscreen hero/slider             |
|         | 1280×720           | HD mobile friendly                     | YouTube embed, banner              |
|         | 960×540            | Optimized low-res                      | Section background mobile          |
| **21:9**| 2100×900           | Ultra-wide (wide screen)               | Modern full-bleed header           |
|         | 1680×720           | Lighter ultra-wide                     | Promo header with text overlay     |

---

## 🛠️ Export Format & Kompresi

| Format | Quality (%) | Ukuran File | Keterangan                       |
|--------|-------------|-------------|----------------------------------|
| WebP   | 80–90       | ⭐ Sangat kecil | Recommended                     |
| JPG    | 70–80       | Medium      | Backup (high compatibility)      |
| PNG    | N/A         | Besar       | Gunakan hanya jika perlu transparansi |

---

## ⚙️ CSS/MUI Aspect Ratio Example

```tsx
<Box
  component="img"
  src="/image.webp"
  alt="..."
  sx={{
    width: '100%',
    aspectRatio: '16 / 9', // ganti dengan 1 / 1, 4 / 3, dst
    objectFit: 'cover',
    borderRadius: 2,
  }}
/>
